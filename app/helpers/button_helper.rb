module Button<PERSON>elper
  BASE_CLASSES = "inline-flex w-full sm:w-auto items-center gap-x-1.5 rounded-md font-semibold shadow-sm".freeze

  VARIANT_STYLES = {
    default: {
      base: "bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 ring-1 ring-inset ring-gray-300 dark:ring-gray-600",
      hover: "hover:bg-gray-50 dark:hover:bg-gray-700"
    },
    primary: {
      base: "bg-primary-600 dark:bg-primary-700 text-white",
      hover: "hover:bg-primary-700 dark:hover:bg-primary-600",
      focus: "focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-600"
    },
    danger: {
      base: "bg-red-600 dark:bg-red-700 text-white",
      hover: "hover:bg-red-700 dark:hover:bg-red-600",
      focus: "focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600"
    },
    success: {
      base: "bg-green-600 dark:bg-green-700 text-white",
      hover: "hover:bg-green-700 dark:hover:bg-green-600",
      focus: "focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-green-600"
    }
  }.freeze

  SIZE_STYLES = {
    sm: {
      padding: "px-2 py-1",
      text: "text-xs",
      icon: "h-4 w-4"
    },
    md: {
      padding: "px-3 py-2",
      text: "text-sm",
      icon: "h-5 w-5"
    },
    lg: {
      padding: "px-4 py-2",
      text: "text-base",
      icon: "h-6 w-6"
    }
  }.freeze

  def link_button(text, path, options = {})
    variant = options.delete(:variant) || :default
    size = options.delete(:size) || :md
    icon_name = options.delete(:icon)

    variant_styles = VARIANT_STYLES[variant.to_sym]
    size_styles = SIZE_STYLES[size.to_sym]

    button_classes = [
      BASE_CLASSES,
      variant_styles[:base],
      variant_styles[:hover],
      variant_styles[:focus],
      size_styles[:padding],
      size_styles[:text]
    ].compact.join(" ")

    link_to path, class: tw(button_classes, options[:class]), **options do
      render_button_content(text, icon_name, size_styles[:icon])
    end
  end

  private

  def render_button_content(text, icon_name, icon_size)
    capture do
      concat(icon(icon_name, class: icon_size)) if icon_name
      concat content_tag(:span, text)
    end
  end
end
