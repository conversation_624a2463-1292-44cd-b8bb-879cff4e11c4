module ApplicationHelper
  include Pagy::Frontend

  def safe_url(url)
    uri = URI.parse(url)

    uri.to_s if uri.is_a?(URI::HTTP)
  rescue URI::InvalidURIError
    nil
  end

  def nav_link(text, path, prefixes: nil, active_class: nil, inactive_class: nil, **link_options)
    # Determine if the link is active
    is_active = if prefixes
      Array(prefixes).any? { |prefix| request.path.start_with?(prefix) }
    else
      current_page?(path)
    end

    # Default classes with mobile-first approach
    default_active_class = "block rounded-md px-3 py-2 text-base font-medium bg-gray-900 text-white"
    default_inactive_class = "block rounded-md px-3 py-2 text-base font-medium text-gray-300 hover:bg-gray-700 hover:text-white"

    # Use custom classes if provided, otherwise use defaults
    class_name = if is_active
      active_class || default_active_class
    else
      inactive_class || default_inactive_class
    end

    # Merge the class with any existing classes in link_options
    link_options[:class] = [ class_name, link_options[:class] ].compact.join(" ")

    # Create the link with all options
    link_to(text, path, **link_options)
  end

  def avatar(user, options = {})
    size = options[:size] || "h-8 w-8"
    class_names = "#{size} rounded-full #{options[:class]}"

    content = if user.avatar.attached?
      image_tag user.avatar.variant(:thumb), class: class_names
    else
      content_tag :canvas, nil,
                  data: {
                    avatar_target: "canvas",
                    name: user.name
                  },
                  class: class_names
    end

    if !user.avatar.attached?
      content_tag :div, content, data: { controller: "avatar" }
    else
      content
    end
  end

  def badge(text, color = :gray, **options)
    color_classes = {
      gray: "bg-gray-50 text-gray-600 ring-gray-500/10 dark:bg-gray-400/10 dark:text-gray-400 dark:ring-gray-400/20",
      red: "bg-red-50 text-red-700 ring-red-600/10 dark:bg-red-400/10 dark:text-red-400 dark:ring-red-400/20",
      yellow: "bg-yellow-50 text-yellow-800 ring-yellow-600/20 dark:bg-yellow-400/10 dark:text-yellow-400 dark:ring-yellow-400/20",
      green: "bg-green-50 text-green-700 ring-green-600/20 dark:bg-green-400/10 dark:text-green-400 dark:ring-green-400/20",
      blue: "bg-blue-50 text-blue-700 ring-blue-700/10 dark:bg-blue-400/10 dark:text-blue-400 dark:ring-blue-400/20",
      indigo: "bg-indigo-50 text-indigo-700 ring-indigo-700/10 dark:bg-indigo-400/10 dark:text-indigo-400 dark:ring-indigo-400/20",
      purple: "bg-purple-50 text-purple-700 ring-purple-700/10 dark:bg-purple-400/10 dark:text-purple-400 dark:ring-purple-400/20",
      pink: "bg-pink-50 text-pink-700 ring-pink-700/10 dark:bg-pink-400/10 dark:text-pink-400 dark:ring-pink-400/20"
    }

    base_classes = "inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset"
    specific_classes = color_classes[color]

    classes = tw(
      base_classes,
      specific_classes,
      options[:class]
    )

    options.delete(:class)

    content_tag :span, text, options.merge(class: classes)
  end

  def tooltip(trigger_content = nil, tooltip_content = nil, options = {}, &block)
    options = {
      position: :right,      # :right, :left, :top, :bottom
      trigger_classes: "group relative flex items-center justify-center rounded py-1.5 hover:cursor-help",
      tooltip_classes: "invisible absolute z-10 rounded bg-gray-800 dark:bg-gray-700 px-2 py-1.5 text-xs font-medium text-white group-hover:visible whitespace-nowrap",
      distance: 16          # distance from trigger in pixels
    }.merge(options)

    position_classes = case options[:position]
    when :right
      "start-full top-1/2 -translate-y-1/2 ms-#{options[:distance]/4}"
    when :left
      "end-full top-1/2 -translate-y-1/2 me-#{options[:distance]/4}"
    when :top
      "bottom-full start-1/2 -translate-x-1/2 mb-#{options[:distance]/4}"
    when :bottom
      "top-full start-1/2 -translate-x-1/2 mt-#{options[:distance]/4}"
    end

    content_tag :div, class: options[:trigger_classes] do
      concat(
        if block_given?
          capture(&block)
        else
          trigger_content
        end
      )
      concat(
        content_tag(:div, tooltip_content, class: "#{options[:tooltip_classes]} #{position_classes}")
      )
    end
  end

  def toggle_switch_button(form, field, options = {})
    options = {
      controller: "toggle-switch",
      action: "click->toggle-switch#toggle",
      active_class: "bg-primary-600",
      inactive_class: "bg-gray-200",
      label_id: "availability-label",
      description_id: "availability-description",
      initial_state: false,
      auto_submit: false,
      data: {}
    }.merge(options)

    content_tag(:button,
      type: "button",
      class: "relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-600 focus:ring-offset-2",
      data: {
        controller: options[:controller],
        action: options[:action],
        "toggle-switch-active-class": options[:active_class],
        "toggle-switch-inactive-class": options[:inactive_class]
      },
      role: "switch",
      aria: {
        checked: options[:initial_state],
        labelledby: options[:label_id],
        describedby: options[:description_id]
      }
    ) do
      concat(
        form.hidden_field(field, data: options[:data].merge({ "toggle-switch-target": "input" }))
      )
      concat(
        content_tag(:span,
          "",
          class: "pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out",
          data: {
            "toggle-switch-target": "toggle",
            "translate-active": "translate-x-5",
            "translate-inactive": "translate-x-0"
          },
          aria: { hidden: true }
        )
      )
      if options[:auto_submit]
        concat(
          form.submit(style: "display: none;", data: { "toggle-switch-target": "submit" })
        )
      end
    end
  end

  def tabs_navigation(tabs, current_tab = request.path.split("/").last)
    content_tag :nav, class: "flex space-x-4", aria: { label: "Tabs" } do
      safe_join(
        tabs.map do |tab_key, tab_data|
          link_to tab_data[:name],
                  tab_data[:path],
                  class: tab_classes(tab_key == current_tab),
                  aria: (tab_key == current_tab ? { current: "page" } : {})
        end
      )
    end
  end

  def safe_external_url(url)
    return nil if url.blank?

    uri = URI.parse(url)
    return nil unless uri.is_a?(URI::HTTP) || uri.is_a?(URI::HTTPS)

    uri.to_s
  rescue URI::InvalidURIError
    nil
  end

  def spinner(size: :sm, color: :primary)
    height_class = nil
    width_class = nil
    color_class = nil

    case size
    when :sm
      height_class = "h-3"
      width_class = "w-3"
    when :md
      height_class = "h-5"
      width_class = "w-5"
    when :lg
      height_class = "h-8"
      width_class = "w-8"
    when :xl
      height_class = "h-10"
      width_class = "w-10"
    else
      raise ArgumentError, "Invalid size: #{size}. Valid sizes are :sm, :md, :lg, :xl."
    end

    case color
    when :primary
      color_class = "text-primary-400"
    when :blue
      color_class = "text-blue-400"
    when :red
      color_class = "text-red-400"
    when :green
      color_class = "text-green-400"
    when :white
      color_class = "text-white"
    else
      raise ArgumentError, "Invalid color: #{color}. Valid colors are :gray, :blue, :red, :green."
    end

    # Build the class string
    classes = "animate-spin #{height_class} #{width_class} #{color_class}"

    content_tag(:svg, class: classes, xmlns: "http://www.w3.org/2000/svg", fill: "none", viewBox: "0 0 24 24") do
      concat content_tag(:circle, nil, class: "opacity-25", cx: "12", cy: "12", r: "10",
                         stroke: "currentColor", "stroke-width": "4")
      concat content_tag(:path, nil, class: "opacity-75", fill: "currentColor",
                         d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z")
    end
  end

  private

  def tab_classes(current)
    base_classes = "rounded-md px-3 py-2 text-sm font-medium"

    if current
      "#{base_classes} bg-indigo-100 text-indigo-700"
    else
      "#{base_classes} text-gray-500 hover:text-gray-700"
    end
  end
end
