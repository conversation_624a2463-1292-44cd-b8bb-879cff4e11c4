<div class="bg-white dark:bg-gray-900 py-4 border-b border-gray-200 dark:border-gray-800">
  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <section aria-labelledby="filter-heading">
      <%= form_with model: @query, scope: :query, url: clients_path, method: :get, data: { controller: "auto-submit" } do |form| %>
        <div class="flex items-center justify-between">
          <!-- Left side filters -->
          <div class="flex items-center space-x-3">
          </div>

          <!-- Right side filters -->
          <div class="flex items-center space-x-3">
            <!-- Enhanced Search -->
            <div data-controller="search-expand" class="relative">
              <div class="flex items-center">
                <%= form.text_field :name_cont,
                                    placeholder: "Search by name",
                                    data: {
                                      search_expand_target: "input",
                                      action: "focus->search-expand#expand blur->search-expand#collapse",
                                    },
                                    class: "w-40 focus:w-64 transition-all duration-300 ease-in-out rounded-md border-0 bg-white dark:bg-gray-700 px-3 py-2 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 ring-1 ring-inset ring-gray-300 dark:ring-gray-600 focus:ring-2 focus:ring-primary-500 text-sm pr-10" %>
                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <%= icon "magnifying-glass", set: "mini", class: "h-5 w-5 text-gray-600 dark:text-gray-400" %>
                </div>
              </div>
            </div>
          </div>
        </div>
        <%= form.submit "filter", class: "hidden", id: "filter-submit" %>
      <% end %>
    </section>
  </div>
</div>
