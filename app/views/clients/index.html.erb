<% content_for :title, "Clients" %>

<div class="w-full h-full flex flex-col bg-gray-50 dark:bg-gray-950">
  <% if notice.present? %>
    <%= alerting :info, notice %>
  <% end %>

  <header class="border-b dark:border-gray-800 bg-white dark:bg-gray-900 shadow-sm sticky top-0 z-10">
    <div class="max-w-8xl px-4 h-14 flex items-center sm:px-6 2xl:px-12 mx-auto">
      <div class="flex items-center justify-between w-full">
        <div class="flex items-center space-x-2">
          <%= image_tag "feedmob-logo-full-white-1000px.png", class: "mx-auto h-7 w-auto hidden dark:block", alt: "feedmob logo" %>
          <span class="inline-block border border-primary-700 rounded-md px-1.5 py-0.5 text-xs font-medium text-primary-700 dark:text-primary-300 tracking-wide">
            clients
          </span>
        </div>
      </div>
    </div>
  </header>

  <div class="grow">
    <div class="mx-auto max-w-8xl px-4 sm:px-6 2xl:px-12 py-4">
      <!-- Search and filters card -->
      <div class="bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-800 mb-4 p-3">
        <%= form_with model: @query, scope: :query, url: clients_path, method: :get, data: { controller: "auto-submit" } do |form| %>
          <div class="flex flex-wrap gap-3 items-center">
            <!-- Search input -->
            <div class="relative rounded-md shadow-sm flex-grow min-w-[200px] max-w-sm">
              <%= form.text_field :name_cont, 
                class: "block w-full rounded-md border-0 py-2 px-3 text-gray-900 dark:text-white ring-1 ring-inset ring-gray-300 dark:ring-gray-700 placeholder:text-gray-400 dark:placeholder:text-gray-500 focus:ring-2 focus:ring-inset focus:ring-primary-600 dark:focus:ring-primary-500 bg-white dark:bg-gray-800 sm:text-sm sm:leading-6", 
                placeholder: "Search by client name" %>
              <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                <%= icon "magnifying-glass", class: "h-4 w-4 text-gray-400 dark:text-gray-500" %>
              </div>
            </div>

            <!-- Campaigns filter -->
            <div class="flex items-center gap-2">
              <label for="active_status" class="text-sm font-medium text-gray-700 dark:text-gray-300 whitespace-nowrap">Activity Status</label>
              <%= form.select :active_status,
                options_for_select([
                  ["All Clients", ""],
                  ["Active (spent in last 2 months)", "active"],
                  ["Inactive (no spend in last 2 months)", "inactive"]
                ], @query.active_status),
                {},
                class: "block rounded-md border-0 py-1.5 pl-3 pr-8 text-gray-900 dark:text-white ring-1 ring-inset ring-gray-300 dark:ring-gray-700 focus:ring-2 focus:ring-primary-600 dark:focus:ring-primary-500 bg-white dark:bg-gray-800 sm:text-sm sm:leading-6",
                data: { action: "change->auto-submit#submit" }
              %>
            </div>

            <!-- Hide Test Clients -->
            <label class="flex items-center space-x-2 cursor-pointer">
              <%= form.check_box :hide_test_data,
                  class: "h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:focus:ring-primary-400",
                  data: { action: "change->auto-submit#submit" } %>
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300 whitespace-nowrap">
                Hide Test Clients
              </span>
            </label>

            <div class="ml-auto text-xs text-gray-500 dark:text-gray-400">
              <% if @clients.any? %>
                <span class="font-medium text-gray-700 dark:text-gray-300"><%= @clients.count %></span> 
                <%= 'client'.pluralize(@clients.count) %> found
              <% else %>
                No clients found
              <% end %>
            </div>
          </div>

          <%= form.submit "Filter", class: "hidden" %>
        <% end %>
      </div>

      <!-- Card Grid Layout -->
      <div class="flex-1 min-h-0">
        <% if @clients.any? %>
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            <% @clients.each do |client| %>
              <%
                # Group mobile apps by name to avoid duplicates across platforms
                unique_apps = client.mobile_apps.group_by(&:name).map { |name, apps| apps.first }
                display_apps = unique_apps.first(4) # Show max 4 apps
                remaining_count = [unique_apps.count - 4, 0].max
              %>
              
              <div class="flex flex-col h-full p-4 bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md hover:border-gray-300 dark:hover:border-gray-600 transition-all duration-200">
                <!-- Header with logo and name -->
                <div class="flex items-center space-x-3 mb-3">
                  <% if client.logo.attached? %>
                    <% bg_class = client.logo_bg_light? ? "bg-white" : "bg-gray-100 dark:bg-gray-800" %>
                    <div class="flex-shrink-0 h-10 flex items-center justify-center rounded-md overflow-hidden <%= bg_class %> p-1.5 border border-gray-100 dark:border-gray-800">
                      <%= image_tag client.logo.variant(format: :webp), class: "max-h-8 w-auto max-w-20 object-contain" %>
                    </div>
                  <% else %>
                    <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-md bg-gradient-to-br from-primary-100 to-primary-200 dark:from-primary-900 dark:to-primary-800 text-primary-700 dark:text-primary-300 text-base font-medium shadow-sm">
                      <%= client.name[0..1].upcase %>
                    </div>
                  <% end %>

                  <div class="min-w-0 flex-1">
                    <div class="flex items-center space-x-2">
                      <h3 class="text-sm font-semibold text-gray-900 dark:text-white truncate">
                        <%= client.name %>
                      </h3>
                      <% if client.is_test %>
                        <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-amber-100 text-amber-700 dark:bg-amber-900/50 dark:text-amber-300">
                          <%= icon "beaker", class: "h-3 w-3 mr-0.5" %>
                          Test
                        </span>
                      <% end %>
                    </div>
                  </div>
                </div>
                
                <!-- Client info section - Show either headline or website, prioritize headline -->
                <% if client.headline.present? || client.website.present? %>
                  <div class="mb-4">
                    <% if client.headline.present? %>
                      <p class="text-sm text-gray-700 dark:text-gray-300 line-clamp-3">
                        <%= client.headline %>
                      </p>
                    <% elsif client.website.present? %>
                      <div class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                        <%= icon "globe-alt", class: "h-4 w-4 flex-shrink-0" %>
                        <span class="truncate"><%= URI.parse(client.website).host rescue client.website %></span>
                      </div>
                    <% end %>
                  </div>
                <% else %>
                  <div class="mb-4">
                    <p class="text-sm text-gray-500 dark:text-gray-400">No additional information available</p>
                  </div>
                <% end %>
                
                <!-- Mobile Apps section - This will grow to fill available space -->
                <div class="flex-1 mb-3">
                  <% if unique_apps.any? %>
                    <div class="flex items-center space-x-2">
                      <% display_apps.each_with_index do |mobile_app, index| %>
                        <div class="relative transition-all duration-200 hover:scale-110">
                          <% if mobile_app.logo.attached? %>
                            <div class="h-8 w-8 rounded-lg overflow-hidden shadow-sm border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hover:shadow-md transition-shadow">
                              <%= image_tag mobile_app.logo.variant(resize_to_limit: [32, 32]), 
                                  class: "h-full w-full object-cover",
                                  title: mobile_app.name %>
                            </div>
                          <% else %>
                            <div class="h-8 w-8 rounded-lg bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 flex items-center justify-center text-xs font-medium text-gray-600 dark:text-gray-300 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow"
                                 title="<%= mobile_app.name %>">
                              <%= mobile_app.name[0].upcase %>
                            </div>
                          <% end %>
                        </div>
                      <% end %>
                      
                      <!-- Show remaining count if there are more apps -->
                      <% if remaining_count > 0 %>
                        <div class="relative h-8 w-8 rounded-lg bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 flex items-center justify-center text-xs font-medium text-gray-600 dark:text-gray-300 shadow-sm hover:shadow-md transition-shadow"
                             title="<%= remaining_count %> more apps">
                          +<%= remaining_count %>
                        </div>
                      <% end %>
                    </div>
                  <% end %>
                </div>
                
                <!-- Footer - Always at bottom -->
                <div class="flex items-center justify-between pt-2 border-t border-gray-100 dark:border-gray-700 mt-auto">
                  <div class="flex items-center space-x-2">
                    <div class="h-1.5 w-1.5 rounded-full bg-green-400"></div>
                    <span class="text-xs text-gray-500 dark:text-gray-400">
                      <%= time_ago_in_words client.created_at %> ago
                    </span>
                  </div>
                  
                  <div class="flex items-center space-x-1">
                    <% if client.website.present? %>
                      <a href="<%= client.website %>" target="_blank" rel="noopener noreferrer"
                        class="inline-flex items-center justify-center h-8 w-8 rounded-md bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700 hover:text-gray-700 dark:hover:text-gray-300 transition-colors" 
                        title="Visit website">
                        <%= icon "globe-alt", class: "h-4 w-4" %>
                      </a>
                    <% end %>
                    
                    <%= link_to client, 
                        class: "inline-flex items-center justify-center h-8 w-8 rounded-md bg-primary-100 dark:bg-primary-900/50 text-primary-600 dark:text-primary-400 hover:bg-primary-200 dark:hover:bg-primary-800 hover:text-primary-700 dark:hover:text-primary-300 transition-colors", 
                        title: "View details" do %>
                      <%= icon "eye", class: "h-4 w-4" %>
                    <% end %>
                  </div>
                </div>
              </div>
            <% end %>
          </div>

        <% else %>
          <div class="bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-800 p-8">
            <div class="flex flex-col items-center justify-center text-center">
              <%= icon "building-office", class: "h-12 w-12 text-gray-400 dark:text-gray-600 mb-3" %>
              <h3 class="font-medium text-gray-900 dark:text-white text-base mb-1">No clients found</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">Try adjusting your search or filter criteria</p>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>
