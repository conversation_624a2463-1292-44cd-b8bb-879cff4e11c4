<div class="bg-white dark:bg-gray-900 overflow-hidden rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm">
  <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-800 flex justify-between items-center">
    <div class="flex items-center gap-3">
      <% if @client.logo.attached? %>
        <% bg_class = @client.logo_bg_light? ? "bg-gray-50" : "bg-gray-100 dark:bg-gray-800" %>
        <%= image_tag @client.logo.variant(format: :webp),
                      class: "h-12 rounded-lg object-contain #{bg_class} px-3 py-2 border border-gray-200 dark:border-gray-700",
                      alt: "#{@client.name} logo" %>
      <% else %>
        <div class="flex-shrink-0 h-12 w-12 flex items-center justify-center rounded-lg bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 text-lg font-medium">
          <%= @client.name[0..1].upcase %>
        </div>
      <% end %>

      <div>
        <h3 class="text-lg font-medium text-white"><%= @client.name %></h3>
        <div class="flex items-center mt-1 gap-2">
          <span class="text-xs text-gray-400 font-mono">ID: <%= @client.id %></span>
          <% if @client.is_test %>
            <span class="inline-flex items-center rounded-full bg-purple-900/50 px-2 py-0.5 text-xs font-medium text-purple-300">
              <%= icon "beaker", class: "mr-1 h-3 w-3" %>Test Client
            </span>
          <% end %>
          <% if @client.headline.present? %>
            <span class="inline-flex items-center rounded-full bg-blue-900/50 px-2 py-0.5 text-xs font-medium text-blue-300">
              <%= @client.headline %>
            </span>
          <% end %>
        </div>
      </div>
    </div>

    <% if @client.active %>
      <span class="inline-flex items-center rounded-full bg-green-900/50 px-3 py-1 text-sm font-medium text-green-300">
        <%= icon "user", class: "mr-1.5 h-4 w-4" %>Active
      </span>
    <% else %>
      <span class="inline-flex items-center rounded-full bg-red-900/50 px-3 py-1 text-sm font-medium text-red-300">
        <%= icon "x-circle", class: "mr-1.5 h-4 w-4" %>Inactive
      </span>
    <% end %>
  </div>

  <div class="px-6 py-5">
    <dl class="grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-5">
      <% if @client.website.present? %>
        <div>
          <dt class="text-xs font-medium text-gray-400 uppercase tracking-wide flex items-center">
            <%= icon "globe-alt", class: "mr-1.5 h-3.5 w-3.5" %>Website
          </dt>
          <dd class="mt-1.5 text-sm text-white flex items-center">
            <a href="<%= @client.website %>" target="_blank" class="inline-flex items-center text-primary-400 hover:text-primary-300">
              <span class="truncate"><%= @client.website.gsub(/^https?:\/\/(www\.)?/, "") %></span>
              <%= icon "arrow-top-right-on-square", class: "ml-1 h-3.5 w-3.5" %>
            </a>

            <%= button_to sync_from_website_client_path(@client),
                          method: :post,
                          class: "ml-3 inline-flex items-center rounded-md border border-gray-700 bg-gray-800 px-2.5 py-1 text-xs font-medium text-gray-300 shadow-sm hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",
                          title: "Sync data from website",
                          data: { turbo_confirm: "This will attempt to extract client data from the website. Continue?" } do %>
              <%= icon "arrow-path", class: "h-3.5 w-3.5 mr-1" %>
              Sync
            <% end %>
          </dd>
        </div>
      <% end %>

      <% if @client.email_suffix.present? %>
        <div>
          <dt class="text-xs font-medium text-gray-400 uppercase tracking-wide flex items-center">
            <%= icon "at-symbol", class: "mr-1.5 h-3.5 w-3.5" %>Email Domain
          </dt>
          <dd class="mt-1.5 text-sm text-white font-mono">
            <%= @client.email_suffix %>
          </dd>
        </div>
      <% end %>

      <div>
        <dt class="text-xs font-medium text-gray-400 uppercase tracking-wide flex items-center">
          <%= icon "identification", class: "mr-1.5 h-3.5 w-3.5" %>Legacy ID
        </dt>
        <dd class="mt-1.5 text-sm text-white">
          <%= link_to legacy_url("clients", @client.legacy_id), target: "_blank", class: "inline-flex items-center gap-x-1 text-primary-400 hover:text-primary-300 font-mono" do %>
            <span><%= @client.legacy_id %></span>
            <%= icon "arrow-top-right-on-square", class: "h-3.5 w-3.5" %>
          <% end %>
        </dd>
      </div>

      <div>
        <dt class="text-xs font-medium text-gray-400 uppercase tracking-wide flex items-center">
          <%= icon "calendar", class: "mr-1.5 h-3.5 w-3.5" %>Created
        </dt>
        <dd class="mt-1.5 text-sm text-white">
          <%= @client.created_at.strftime("%B %d, %Y") %>
          <span class="ml-1.5 text-xs text-gray-400">(<%= time_ago_in_words @client.created_at %> ago)</span>
        </dd>
      </div>

      <div>
        <dt class="text-xs font-medium text-gray-400 uppercase tracking-wide flex items-center">
          <%= icon "clock", class: "mr-1.5 h-3.5 w-3.5" %>Updated
        </dt>
        <dd class="mt-1.5 text-sm text-white">
          <%= @client.updated_at.strftime("%B %d, %Y") %>
          <span class="ml-1.5 text-xs text-gray-400">(<%= time_ago_in_words @client.updated_at %> ago)</span>
        </dd>
      </div>

      <% if @client.client_social_media_links.active.any? %>
        <div class="col-span-full pt-6 border-t border-gray-800">
          <dt class="text-xs font-medium text-gray-400 uppercase tracking-wide flex items-center mb-4">
            <%= icon "share", class: "mr-1.5 h-3.5 w-3.5" %>Social Media
          </dt>
          <div class="flex flex-wrap gap-3">
            <% @client.client_social_media_links.active.each do |social_link| %>
              <% platform_config = social_media_platform_config(social_link.platform) %>
              <a href="<%= social_link.url %>"
                target="_blank"
                class="inline-flex items-center gap-2 px-3 py-2 rounded-lg border border-gray-700 bg-gray-800 text-sm font-medium text-gray-300 hover:bg-gray-700 hover:border-gray-600 transition-colors"
                title="Visit <%= social_link.platform.humanize %> profile">
                <div class="flex-shrink-0 w-4 h-4 <%= platform_config[:color] %>">
                  <%= platform_config[:icon] %>
                </div>
                <span class="truncate max-w-[120px]"><%= social_link.platform.humanize %></span>
                <%= icon "arrow-top-right-on-square", class: "h-3.5 w-3.5 flex-shrink-0" %>
              </a>
            <% end %>
          </div>
        </div>
      <% end %>

    </dl>
  </div>
</div>
