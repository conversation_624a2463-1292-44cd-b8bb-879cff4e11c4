<div class="flex w-full h-screen bg-gray-950">
  <!-- Main Chat Area -->
  <div class="flex-1 flex flex-col min-w-0 bg-gray-950">
    <!-- Messages Container -->
    <div class="flex-1 min-h-0">
      <div class="h-full overflow-y-auto" data-controller="scroll" data-scroll-target="scrollable" data-action="turbo:before-stream-render@window->scroll#toBottom">
        <div class="max-w-3xl mx-auto px-4 sm:px-6 py-4">
          <%= turbo_stream_from "#{dom_id(@conversation)}_messages" %>

          <div id="<%= dom_id(@conversation) %>_messages">
            <%= render @conversation.messages %>
          </div>
        </div>
      </div>
    </div>

    <!-- Message Input Form -->
    <div class="border-t border-gray-800">
      <div class="max-w-3xl mx-auto p-4 sm:px-6">
        <%= render "messages/form", conversation: @conversation %>
      </div>
    </div>
  </div>

  <!-- Side Panel -->
  <div class="w-80 bg-gray-900 border-l border-gray-800 flex flex-col">
    <!-- Context Selection -->
    <div class="p-4 border-b border-gray-800">
      <div class="mb-4">
        <div class="flex items-center gap-2 mb-2">
          <h3 class="text-sm font-semibold text-gray-200">Context Selection</h3>
          <span class="px-2 py-0.5 text-xs font-medium text-orange-400 bg-orange-900/30 border border-orange-700/50 rounded-md">
            ALPHA
          </span>
        </div>
        <p class="text-xs text-gray-400 leading-relaxed">
          Select clients and partners to provide relevant context for your conversation. 
          This helps the assistant understand your business relationships and provide more targeted responses.
        </p>
      </div>
      <%= turbo_frame_tag "#{dom_id(@conversation)}_context" do %>
        <%= render 'conversations/context_selection', conversation: @conversation %>
      <% end %>
    </div>
    
    <!-- Assistant Selection -->
    <div class="p-4 bg-gray-850">
      <div class="mb-4">
        <h3 class="text-sm font-semibold text-gray-200 mb-2">Assistant Configuration</h3>
        <p class="text-xs text-gray-400 leading-relaxed mb-3">
          Choose your preferred assistant personality and AI model. Different assistants have unique expertise areas, 
          while models vary in capabilities and response styles.
        </p>
        <div class="flex items-center gap-2 text-xs text-blue-400 bg-blue-950/30 border border-blue-800/30 rounded-md px-2 py-1">
          <svg class="w-3 h-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
          </svg>
          <span>Changes apply automatically</span>
        </div>
      </div>
      <%= turbo_frame_tag "#{dom_id(@conversation)}_assistant" do %>
        <%= render 'conversations/assistant_selection', 
            conversation: @conversation %>
      <% end %>
    </div>
  </div>
</div>
