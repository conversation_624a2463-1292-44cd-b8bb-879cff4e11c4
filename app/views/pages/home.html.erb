<% # filepath: /Users/<USER>/Projects/assistant/app/views/pages/home.html.erb %>
<% content_for :title, "FeedMob Assistant - Cut Through the Noise" %>

<div class="w-full h-full flex flex-col bg-gray-50 dark:bg-gray-950">
  <header class="border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 shadow-sm sticky top-0 z-10">
    <div class="max-w-7xl px-4 h-16 flex items-center sm:px-6 2xl:px-12 mx-auto">
      <div class="flex items-center justify-center w-full">
        <%= image_tag "feedmob-logo-full-white-1000px.png", class: "h-8 w-auto hidden dark:block", alt: "FeedMob logo" %>
        <%= image_tag "feedmob-logo-full-black-1000px.png", class: "h-8 w-auto block dark:hidden", alt: "FeedMob logo" %>
        <span class="ml-3 inline-block bg-primary-600 dark:bg-primary-800 text-white dark:text-primary-200 text-xs font-semibold px-3 py-1 rounded-full border border-primary-500 dark:border-primary-700/50">Assistant</span>
      </div>
    </div>
  </header>

  <div class="grow overflow-y-auto">
    <div class="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 py-8">
      <!-- Main CTA - AI Assistant -->
      <div class="text-center mb-12">
        <h1 class="text-5xl lg:text-6xl font-black text-white mb-4 tracking-tight">
          Cut Through the <span class="bg-gradient-to-r from-primary-400 to-blue-400 bg-clip-text text-transparent">Noise</span>
        </h1>
        <!-- Animated underline -->
        <div class="relative mb-6">
          <div class="h-1 bg-gradient-to-r from-primary-400 to-blue-400 rounded-full mx-auto max-w-xl animate-[slideInFromLeft_0.5s_ease-out_0.3s_both]"></div>
        </div>
        <p class="text-xl text-gray-300 mb-8 font-medium max-w-2xl mx-auto">
          Sharp tools. Real data. <span class="text-white font-semibold">Better decisions.</span>
        </p>

        <%= link_to new_conversation_path, class: "inline-flex items-center px-10 py-5 bg-gradient-to-r from-primary-600 to-blue-600 hover:from-primary-700 hover:to-blue-700 text-white font-bold rounded-xl transition-all duration-300 transform hover:scale-105 shadow-2xl hover:shadow-3xl text-xl border border-primary-500/50" do %>
          <%= icon "message-circle-heart", library: "lucide", class: "w-7 h-7 mr-3" %>
          Fire Up Assistant
        <% end %>
      </div>

      <!-- Feature Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <!-- Analytics & Workspaces Group -->
        <div class="bg-gray-900 rounded-xl border border-gray-800 p-6">
          <div class="flex items-center mb-4">
            <div class="h-10 w-10 bg-gradient-to-br from-green-500 to-emerald-500 rounded-lg flex items-center justify-center mr-3">
              <%= icon "dollar-sign", library: "lucide", class: "h-6 w-6 text-white" %>
            </div>
            <h3 class="text-lg font-semibold text-white">Numbers Don't Lie</h3>
          </div>

          <div class="space-y-3">
            <%= link_to monthly_campaign_spends_path, class: "flex items-center justify-between p-3 rounded-lg bg-gray-800 hover:bg-gray-700 transition-colors group" do %>
              <span class="text-white font-medium">Campaign Spends</span>
              <%= icon "arrow-right", library: "lucide", class: "w-4 h-4 text-gray-400 group-hover:text-white" %>
            <% end %>

            <%= link_to new_workspace_path, class: "flex items-center justify-between p-3 rounded-lg bg-gray-800 hover:bg-gray-700 transition-colors group" do %>
              <div class="flex items-center">
                <span class="text-white font-medium">AI Workspaces</span>
                <span class="ml-2 inline-block bg-primary-800 text-primary-200 text-xs px-2 py-0.5 rounded-full">Power Mode</span>
              </div>
              <%= icon "arrow-right", library: "lucide", class: "w-4 h-4 text-gray-400 group-hover:text-white" %>
            <% end %>
          </div>
        </div>

        <!-- Client & Partner Management Group -->
        <div class="bg-gray-900 rounded-xl border border-gray-800 p-6">
          <div class="flex items-center mb-4">
            <div class="h-10 w-10 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center mr-3">
              <%= icon "users", library: "lucide", class: "h-6 w-6 text-white" %>
            </div>
            <div class="flex items-center">
              <h3 class="text-lg font-semibold text-white">People First</h3>
              <span class="ml-2 inline-block bg-blue-800 text-blue-200 text-xs px-2 py-0.5 rounded-full">New</span>
            </div>
          </div>

          <div class="space-y-3">
            <%= link_to clients_path, class: "flex items-center justify-between p-3 rounded-lg bg-gray-800 hover:bg-gray-700 transition-colors group" do %>
              <span class="text-white font-medium">Client Hub</span>
              <%= icon "arrow-right", library: "lucide", class: "w-4 h-4 text-gray-400 group-hover:text-white" %>
            <% end %>

            <%= link_to partners_path, class: "flex items-center justify-between p-3 rounded-lg bg-gray-800 hover:bg-gray-700 transition-colors group" do %>
              <span class="text-white font-medium">Partner Network</span>
              <%= icon "arrow-right", library: "lucide", class: "w-4 h-4 text-gray-400 group-hover:text-white" %>
            <% end %>
          </div>
        </div>
      </div>

      <!-- What's New - Separate -->
      <div class="bg-gray-900 rounded-xl border border-gray-800 p-4 mb-6">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <div class="h-8 w-8 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center mr-3">
              <%= icon "zap", library: "lucide", class: "h-4 w-4 text-white" %>
            </div>
            <div>
              <div class="flex items-center gap-2">
                <h3 class="text-base font-semibold text-white">Fresh Off The Press</h3>
                <span class="inline-block bg-green-800 text-green-200 text-xs px-2 py-0.5 rounded-full">June 23, 2025</span>
              </div>
              <p class="text-xs text-gray-400">Campaign Sorting + Margin Display + Media Plan Context</p>
            </div>
          </div>

          <%= link_to whats_new_path, class: "inline-flex items-center px-3 py-1.5 bg-yellow-800 hover:bg-yellow-700 text-yellow-200 font-medium rounded-lg transition-colors text-sm" do %>
            See What's New
            <%= icon "arrow-right", library: "lucide", class: "w-3 h-3 ml-1.5" %>
          <% end %>
        </div>
      </div>

      <!-- Feedback Section -->
      <div class="bg-gray-900 rounded-xl border border-gray-800 p-4 mb-6">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <div class="h-8 w-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mr-3">
              <%= icon "message-square-more", library: "lucide", class: "h-4 w-4 text-white" %>
            </div>
            <div>
              <h3 class="text-base font-semibold text-white">Got Feedback?</h3>
              <p class="text-xs text-gray-400">Share feedback, report bugs, or suggest features</p>
            </div>
          </div>

          <%= link_to "https://app.slack.com/client/T0JTAU2CT/C08AEJ9GS1F", target: "_blank", class: "inline-flex items-center px-3 py-1.5 bg-purple-800 hover:bg-purple-700 text-purple-200 font-medium rounded-lg transition-colors text-sm" do %>
            <%= icon "slack", library: "lucide", class: "w-3 h-3 mr-1.5" %>
            Join Slack
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer -->
  <footer class="mt-auto">
    <div class="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 py-4">
      <div class="text-center text-sm text-gray-400 space-y-2">
        <div>&copy; <%= Date.current.year %> FeedMob Inc.</div>
        <div class="flex items-center justify-center gap-2 text-xs text-gray-500">
          <span>Powered by</span>
          <div class="flex items-center">
            <%= image_tag "bedrock/logo.svg", alt: "AWS Bedrock Logo", class: "h-4 invert" %>
            <%= image_tag "bedrock/text.svg", alt: "AWS Bedrock", class: "h-3 ml-1 invert" %>
          </div>
        </div>
      </div>
    </div>
  </footer>
</div>