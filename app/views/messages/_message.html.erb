<div id="<%= dom_id(message) %>" class="flex gap-3 py-4 text-sm">
  <span class="relative flex-shrink-0">
    <div class="sticky top-0 flex h-8 w-8 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800 border border-gray-300 dark:border-gray-700">
      <% if message.role == "user" %>
        <%= avatar(message.user) %>
      <% else %>
        <%= icon "sparkles", class: "h-5 w-5 text-primary-400" %>
      <% end %>
    </div>
  </span>
  <div class="relative flex-1 min-w-0" data-controller="content">
    <%= render "messages/message_status", message: message %>
    <%= render "messages/message_body", message: message %>
  </div>
</div>