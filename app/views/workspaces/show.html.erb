<% content_for :title, @workspace.display_title %>

<% if Flipper.enabled?(:workspace_maintenance_mode, current_user) %>
  <div class="flex flex-col items-center justify-center h-full w-full px-4 py-12">
    <%= image_tag "feedmob-logo-full-white-1000px.png", class: "h-10 w-auto mb-8 hidden dark:block", alt: "feedmob logo" %>
    <%= image_tag "feedmob-logo-full-white-1000px.png", class: "h-10 w-auto mb-8 block dark:hidden", alt: "feedmob logo" %>

    <div class="bg-amber-100 dark:bg-amber-900 text-amber-800 dark:text-amber-300 px-6 py-5 rounded-lg shadow-sm max-w-md w-full text-center">
      <%= icon "exclamation-triangle", class: "h-7 w-7 mx-auto mb-3" %>
      <h3 class="text-lg font-medium">Maintenance</h3>
      <p class="mt-2">Workspace functionality is currently under maintenance. Come back later.</p>
    </div>
  </div>
<% else %>
  <div class="h-full w-full flex flex-col"
    data-controller="perspective"
    data-perspective-id-value="<%= @workspace.id %>"
  >
    <header class="border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 shadow-sm sticky top-0 z-10">
      <div class="max-w-8xl px-4 h-16 flex items-center sm:px-6 2xl:px-12 mx-auto">
        <div class="flex items-center justify-between w-full">
          <div class="flex items-center space-x-3">
            <%= image_tag "feedmob-logo-full-white-1000px.png", class: "h-7 w-auto hidden dark:block", alt: "feedmob logo" %>
            <%= image_tag "feedmob-logo-full-black-1000px.png", class: "h-7 w-auto block dark:hidden", alt: "feedmob logo" %>
            <div class="h-6 border-r border-gray-300 dark:border-gray-700"></div>
            <div class="flex items-center">
              <span class="mr-2.5 inline-block border border-primary-500 dark:border-primary-700 rounded-md px-2 py-0.5 text-xs font-medium text-primary-700 dark:text-primary-300 tracking-wide">
                workspace
              </span>
              <% if @workspace.title.present? %>
                <div class="flex flex-col">
                  <span class="text-base font-medium text-gray-900 dark:text-white">
                    <%= @workspace.display_title %>
                  </span>
                  <% if @workspace.description.present? %>
                    <span class="text-xs text-gray-400 leading-tight">
                      <%= @workspace.description %>
                    </span>
                  <% end %>
                </div>
              <% else %>
                <span class="ml-2 text-sm text-gray-400">
                  #<%= @workspace.id %>
                </span>
              <% end %>
            </div>
          </div>

          <div class="flex items-center space-x-2">
            <%= render partial: "refresh_timestamp", locals: { task_run: @latest_refresh_task_run } %>

            <!-- Edit Button -->
            <%= link_to edit_workspace_path(@workspace),
                        class: "p-2 text-gray-400 hover:text-gray-300 transition-colors duration-200 rounded-md",
                        title: "Edit workspace" do %>
              <%= icon "pencil", library: "lucide", class: "h-4 w-4" %>
            <% end %>

            <!-- Star Button -->
            <div class="p-2 text-gray-400 hover:text-gray-300 transition-colors duration-200 rounded-md">
              <%= render "favorite_button", workspace: @workspace %>
            </div>

            <!-- Q&A Button -->
            <a href="https://docs.google.com/document/d/15Cdg4ZO6p4pk4QIblNcjKD9mmXCqGPLBSEGAeHgaTUw/edit?usp=sharing"
               target="_blank"
               class="p-2 text-gray-400 hover:text-gray-300 transition-colors duration-200 rounded-md"
               title="Q&A Documentation">
              <%= icon "circle-help", library: "lucide", class: "h-4 w-4" %>
            </a>

            <!-- Save Button -->
            <button class="p-2 text-gray-400 hover:text-gray-300 transition-colors duration-200 rounded-md"
              data-action="perspective#save"
              title="Save workspace"
            >
              <span data-perspective-target="saveButtonIcon">
                <%= icon "save", library: "lucide", class: "h-4 w-4" %>
              </span>
            </button>

            <!-- New Workspace Button -->
            <%= link_to new_workspace_path,
                        class: "p-2 text-gray-400 hover:text-gray-300 transition-colors duration-200 rounded-md",
                        title: "New workspace" do %>
              <%= icon "plus", class: "h-4 w-4" %>
            <% end %>

            <!-- Share Button -->
            <button
              class="p-2 text-gray-400 hover:text-gray-300 transition-colors duration-200 rounded-md"
              data-action="click->perspective#copyShareLink"
              data-perspective-target="shareButton"
              title="Share workspace"
             >
              <%= icon "share-2", library: "lucide", class: "h-4 w-4" %>
            </button>

            <%= render partial: "date_range_filter" %>
          </div>
        </div>
      </div>
    </header>

    <perspective-workspace id="workspace" theme="Pro Dark" class="invisible flex-grow"></perspective-workspace>
    <div class="w-full h-full flex items-center justify-center" data-perspective-target="loader">
      <%= spinner(size: :xl) %>
    </div>

    <div class="bg-gray-950 border-t border-gray-800">
      <div data-perspective-target="messageArea" class="hidden px-4 py-2 text-sm font-medium"></div>
      <form class="px-4 py-1.5 font-mono text-sm flex items-center" data-action="submit->perspective#generate">
        <div class="flex items-center w-full">
          <span class="text-teal-500 mx-1">$</span>
          <input type="text"
                data-perspective-target="terminalInput"
                placeholder="What kind of insights are you looking for? (Press Enter to generate)"
                class="w-full grow bg-transparent border-none text-gray-200 focus:outline-none focus:ring-0 placeholder-gray-600 text-xs font-mono py-0.5"/>
        </div>
        <button type="submit" data-action="perspective#generate" class="ml-2 px-2.5 py-1.5 bg-teal-700/80 hover:bg-teal-600 text-teal-100 rounded-md text-xs transition-all duration-200 ease-in-out flex items-center justify-center shadow-sm hover:shadow hover:translate-y-[-1px]" data-perspective-target="generateButton">
          <%= icon "send-horizontal", library: "lucide", class: "h-5 w-5" %>
          <span class="ml-1.5">Send</span>
        </button>
        <div data-perspective-target="terminalLoader" class="hidden ml-2 py-1">
          <%= spinner(size: :md) %>
        </div>
      </form>
    </div>

  </div>
<% end %>