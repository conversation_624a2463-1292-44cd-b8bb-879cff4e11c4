<div class="w-full">
  <% if notice.present? %>
    <%= alerting :info, notice %>
  <% end %>

  <% content_for :page_heading, "Users" %>

  <% content_for :page_actions do %>
    <%= link_button "New user", new_user_path, icon: "plus", variant: :primary %>
  <% end %>

  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 h-[calc(100vh-var(--header-height)-var(--filters-height))] flex flex-col">
    <div class="flex-1 min-h-0">
      <% if @users.any? %>
        <div class="h-full overflow-y-auto">
          <div class="flow-root">
            <div class="relative">
              <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="sticky top-0 bg-white dark:bg-gray-900 shadow-lg z-10">
                  <tr>
                    <th scope="col" class="py-4 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 sm:pl-3 w-[15%] border-b border-gray-200 dark:border-gray-700">
                      <div class="flex items-center gap-x-2">
                        <%= icon "identification", class: "h-4 w-4 text-indigo-400" %>
                        <span>ID</span>
                      </div>
                    </th>
                    <th scope="col" class="px-3 py-4 text-left text-sm font-semibold text-gray-100 w-[10%] border-b border-gray-700">
                      <span class="sr-only">Avatar</span>
                    </th>
                    <th scope="col" class="px-3 py-4 text-left text-sm font-semibold text-gray-100 w-[30%] border-b border-gray-700">
                      <div class="flex items-center gap-x-2">
                        <%= icon "user", class: "h-4 w-4 text-indigo-400" %>
                        <span>Name</span>
                      </div>
                    </th>
                    <th scope="col" class="px-3 py-4 text-left text-sm font-semibold text-gray-100 w-[20%] border-b border-gray-700">
                      <div class="flex items-center gap-x-2">
                        <%= icon "user-circle", class: "h-4 w-4 text-indigo-400" %>
                        <span>Role</span>
                      </div>
                    </th>
                    <th scope="col" class="px-3 py-4 text-left text-sm font-semibold text-gray-100 w-[15%] border-b border-gray-700">
                      <div class="flex items-center gap-x-2">
                        <%= icon "calendar", class: "h-4 w-4 text-indigo-400" %>
                        <span>Created At</span>
                      </div>
                    </th>
                    <th scope="col" class="px-3 py-4 text-left text-sm font-semibold text-gray-100 w-[10%] border-b border-gray-700">
                      <span class="sr-only">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                  <% @users.each do |user| %>
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                      <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 dark:text-gray-200 sm:pl-3">
                        <%= user.id %>
                      </td>
                      <td class="whitespace-nowrap py-4">
                        <%= avatar user, size: "h-6 w-6" %>
                      </td>
                      <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-700 dark:text-gray-300">
                        <%= user.name %>
                      </td>
                      <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-700 dark:text-gray-300">
                        <%= user.role %>
                      </td>
                      <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-700 dark:text-gray-300">
                        <%= time_ago_in_words user.created_at %>
                      </td>
                      <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-3">
                        <%= link_to user, class: "text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300" do %>
                          View<span class="sr-only">, <%= user.name %></span>
                        <% end %>
                      </td>
                    </tr>
                  <% end %>
                </tbody>
              </table>
            </div>
          </div>
        </div>

      <% else %>
        <div class="flex flex-col items-center justify-center h-full text-center">
          <div class="rounded-full bg-gray-200/50 dark:bg-gray-800/50 p-6 mb-4">
            <%= icon "users", class: "h-12 w-12 text-indigo-600 dark:text-indigo-400" %>
          </div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">No users</h3>
          <p class="mt-2 text-sm text-gray-600 dark:text-gray-400 max-w-sm">
            Users will appear here once created.
          </p>
        </div>
      <% end %>
    </div>

    <div class="py-4 bg-white dark:bg-gray-900">
      <%== pagy_nav(@pagy) if @pagy.pages > 1 %>
    </div>
  </div>
</div>
