<!-- app/views/shared/_combobox.html.erb -->
<div class="<%= html_class %>">
  <div data-controller="combobox" data-combobox-url-value="<%= url %>">
    <%= form.hidden_field method, data: { combobox_target: "hiddenSelect" }.merge(data) %>
    <div class="relative mt-2">
      <input
        data-combobox-target="input"
        data-action="input->combobox#search"
        id="combobox"
        type="text"
        class="w-full mt-2 block rounded-md border-0 px-3 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800 ring-1 ring-inset ring-gray-300 dark:ring-gray-600 focus:ring-2 focus:ring-primary-600 sm:text-sm sm:leading-6"
        role="combobox"
        placeholder="<%= placeholder %>"
        value="<%= selected %>"
        aria-controls="options"
        aria-expanded="false"
      >
      <button
        type="button"
        data-combobox-target="toggleButton"
        data-action="click->combobox#toggleList"
        class="absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none"
      >
        <%= icon "chevron-up-down", class: "h-5 w-5 text-gray-400" %>
      </button>

      <button
        type="button"
        data-combobox-target="removeButton"
        data-action="click->combobox#clearSelection"
        class="absolute inset-y-0 right-0 hidden items-center rounded-r-md px-2 focus:outline-none"
      >
        <%= icon "x-circle", class: "h-5 w-5 text-gray-400" %>
      </button>

      <ul
        data-combobox-target="list"
        class="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm hidden"
        id="options"
        role="listbox"
      >
        <!-- Options will be populated dynamically -->
      </ul>
    </div>
  </div>
</div>
