<div class="flex z-[100] relative">
  <div class="flex h-screen w-16 flex-col justify-between border-e bg-gray-900 border-gray-800 overflow-visible">
    <div>
      <%# Logo/Brand %>
      <%= link_to root_path do %>
        <div class="inline-flex h-16 w-16 items-center justify-center"> <%# Fixed height to match header %>
          <%= image_tag "feedmob-logo-plus-1000px.png", class: "mx-auto h-6 w-auto", alt: "feedmob logo" %>
        </div>
      <% end %>

      <div class="border-t border-gray-800">
        <div class="px-2">
          <%= render "shared/sidebar_icons" %>
        </div>
      </div>
    </div>

    <div class="sticky inset-x-0 bottom-0 border-t border-gray-800 bg-gray-900">
      <%# User Profile Button %>
      <div class="p-2">
        <div class="group relative flex w-full justify-center rounded-lg px-2 py-1.5 text-sm text-gray-400 hover:bg-gray-800 hover:text-gray-200">
          <%# If user has avatar %>
          <%= avatar current_user %>
          <span class="invisible absolute start-full top-1/2 ms-4 -translate-y-1/2 rounded bg-gray-700 px-2 py-1.5 text-xs font-medium text-white group-hover:visible">
            <%= current_user.name %>
          </span>
        </div>
      </div>

      <%# Logout button %>
      <div class="p-2">
        <%= button_to session_path, method: :delete, 
            class: "group relative flex w-full justify-center rounded-lg px-2 py-1.5 text-sm text-gray-400 hover:bg-gray-800 hover:text-gray-200" do %>
          <%= icon "arrow-right-end-on-rectangle", class: "h-5 w-5" %>
          <span class="invisible absolute start-full top-1/2 ms-4 -translate-y-1/2 rounded bg-gray-700 px-2 py-1.5 text-xs font-medium text-white group-hover:visible">
            Logout
          </span>
        <% end %>
      </div>
    </div>
  </div>
</div>
