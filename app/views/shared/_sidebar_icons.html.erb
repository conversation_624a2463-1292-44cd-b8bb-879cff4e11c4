<%# Keep original button menu %>
<div class="py-4 space-y-1">
  <%= button_to conversations_path, method: :post, class: "w-full group relative flex justify-center rounded bg-primary-800 hover:bg-primary-900 px-2 py-1.5 text-primary-200" do %>
    <%= icon "plus", class: "h-5 w-5" %>
    <span class="whitespace-nowrap invisible absolute start-full top-1/2 ms-4 -translate-y-1/2 rounded bg-gray-700 px-2 py-1.5 text-xs font-medium text-white group-hover:visible">
      New conversation
    </span>
  <% end %>
</div>

<%# Configured menu items %>
<ul class="space-y-1 border-t border-gray-800 pt-4">
  <% MenuConfiguration.items.each do |key, item| %>
    <% next if item[:condition] && !instance_exec(&item[:condition]) %>
    
    <% 
      # Check if the feature is newly released (within the last 30 days)
      is_new_feature = false
      if item[:release_date].present?
        release_date = item[:release_date].is_a?(String) ? Date.parse(item[:release_date]) : item[:release_date]
        is_new_feature = (Date.today - release_date).to_i <= 30
      end
    %>
    
    <% case item[:type] %>
    <% when :link %>
      <li>
        <%= link_to send(item[:path]),
            class: "group relative flex justify-center rounded-md px-2 py-1.5 
                   #{current_page?(send(item[:path])) ? 'bg-gray-800 text-gray-100' : 'text-gray-400'}
                   hover:bg-gray-800 hover:text-gray-100
                   transition-all duration-150" do %>
          <%= icon item[:icon], **item.slice(:library).compact, class: "h-5 w-5" %>
          <%= render "shared/tooltip", text: item[:label] %>
          <% if is_new_feature %>
            <span class="absolute -top-1 -right-1 flex h-4 w-4">
              <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-primary-400 opacity-75"></span>
              <span class="relative inline-flex rounded-full h-4 w-4 bg-primary-500 text-white text-[8px] font-bold items-center justify-center">NEW</span>
            </span>
          <% end %>
        <% end %>
      </li>

    <% when :dropdown %>
      <li data-controller="dropdown" class="relative">
        <div data-action="click->dropdown#toggle"
            class="cursor-pointer relative flex justify-center rounded-md px-2 py-1.5
                   text-gray-400
                   hover:bg-gray-800
                   hover:text-gray-100
                   transition-all duration-150">
          <%= icon item[:icon], **item.slice(:library).compact, class: "h-5 w-5" %>
          <% if is_new_feature %>
            <span class="absolute -top-1 -right-1 flex h-4 w-4">
              <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-primary-400 opacity-75"></span>
              <span class="relative inline-flex rounded-full h-4 w-4 bg-primary-500 text-white text-[8px] font-bold items-center justify-center">NEW</span>
            </span>
          <% end %>
        </div>

        <ul data-dropdown-target="menu" 
            class="hidden z-50 absolute start-full top-1/2 ms-4 -translate-y-1/2
                   min-w-[160px] max-w-[200px]
                   bg-gray-800
                   shadow-lg rounded-md border border-gray-700
                   backdrop-blur-sm
                   py-1">
          <% item[:items].each do |sub_key, sub_item| %>
            <% 
              # Check if the submenu item is newly released
              sub_is_new = false
              if sub_item[:release_date].present?
                sub_release_date = sub_item[:release_date].is_a?(String) ? Date.parse(sub_item[:release_date]) : sub_item[:release_date]
                sub_is_new = (Date.today - sub_release_date).to_i <= 30
              end
            %>
            <li>
              <%= link_to sub_item[:path].is_a?(Symbol) ? send(sub_item[:path]) : sub_item[:path], 
                  class: "flex items-center gap-2 px-3 py-1.5
                         text-gray-200
                         hover:bg-gray-700
                         transition-all duration-150 relative" do %>
                <%= icon sub_item[:icon], **sub_item.slice(:library).compact, class: "h-4 w-4" %>
                <span class="text-sm font-medium"><%= sub_item[:label] %></span>
                <% if sub_is_new %>
                  <span class="ml-1 inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-primary-800 text-primary-100">
                    New
                  </span>
                <% end %>
              <% end %>
            </li>
          <% end %>
        </ul>
      </li>
    <% end %>
  <% end %>
</ul>
