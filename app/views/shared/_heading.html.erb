<% if content_for?(:page_heading) || content_for?(:page_actions) %>
  <header class="border-b border-gray-800 bg-gray-900 shadow-sm mt-6 md:mt-0">
    <div class="max-w-7xl px-4 h-16 flex items-center sm:px-6 lg:px-8 mx-auto">
      <div class="flex flex-col md:flex-row md:items-center md:justify-between w-full">
        <div class="flex items-center gap-3">
          <h2 class="text-2xl font-bold text-gray-100 sm:text-3xl sm:tracking-tight">
            <%= yield :page_heading %>
          </h2>
          <% if content_for?(:page_stage) %>
            <span class="inline-flex items-center rounded-md bg-gray-800 px-2.5 py-0.5 text-sm font-medium text-gray-200">
              <%= yield :page_stage %>
            </span>
          <% end %>
        </div>
        <div class="mt-4 md:mt-0 md:ml-4 flex flex-col md:flex-row gap-2 w-full md:w-auto">
          <%= yield :page_actions %>
        </div>
      </div>
    </div>
  </header>
<% end %>
