<div data-controller="dropdown filter-counter filter-typeahead" class="relative inline-block text-left <%= options[:width_full] ? "w-full" : "" %>">
  <button type="button"
          data-action="dropdown#toggle"
          class="<%= options[:width_full] ? "w-full justify-between" : "" %> inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 bg-gray-100 dark:bg-gray-800 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-150">
    <div class="flex items-center">
      <% if local_assigns[:icon_name] %>
        <%= icon local_assigns[:icon_name], library: "lucide", class: "h-4 w-4 mr-2" %>
      <% end %>
      <span><%= label %></span>
      <span data-filter-counter-target="count"
            class="hidden ml-1.5 rounded bg-gray-700 px-1.5 py-0.5 text-xs font-semibold tabular-nums text-gray-300">0</span>
    </div>
    <%= icon "chevron-down", set: "mini", class: "-mr-1 ml-1.5 h-5 w-5 text-gray-400" %>
  </button>

  <div data-dropdown-target="menu"
      data-transition-enter-from="opacity-0 scale-95"
      data-transition-enter-to="opacity-100 scale-100"
      data-transition-leave-from="opacity-100 scale-100"
      data-transition-leave-to="opacity-0 scale-95"
      class="hidden absolute right-0 z-30 mt-2 w-72 origin-top-right rounded-md bg-white dark:bg-gray-800 p-4 shadow-lg ring-1 ring-gray-200 dark:ring-gray-700 focus:outline-none">
    <div class="space-y-3">
      <div class="flex justify-between items-center">
        <div class="relative flex-1 ml-2">
          <%= form.text_field "#{field}_search".to_sym,
                              placeholder: "Search...",
                              class: "w-full px-2 py-1 text-xs bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-gray-200 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-primary-500",
                              data: {
                                action: "input->filter-typeahead#filterItems",
                                filter_typeahead_target: "input",
                              } %>
          <%= icon "magnifying-glass", class: "absolute right-2 top-1.5 h-3 w-3 text-gray-400" %>
        </div>
      </div>

      <!-- Add this after the search field and before the collection_check_boxes -->
      <% unless options[:hide_select_all] %>
        <div class="flex items-center group hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md px-2 py-1 transition-colors duration-150 border-b border-gray-200 dark:border-gray-700 mb-2">
          <%= check_box_tag "select_all", "1", false,
                            data: {
                              action: "change->filter-counter#toggleAll",
                              filter_counter_target: "selectAll",
                            },
                            class: "h-4 w-4 rounded border-gray-300 dark:border-gray-600 text-primary-500 focus:ring-primary-500 focus:ring-offset-white dark:focus:ring-offset-gray-800 bg-white dark:bg-gray-700" %>
          <label for="select_all" class="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-gray-200">
            Select All
          </label>
        </div>
      <% end %>

      <div class="overflow-y-auto space-y-1 max-h-60 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-700 scrollbar-track-gray-100 dark:scrollbar-track-gray-900">
        <%= form.collection_check_boxes "#{field}".to_sym, collection, options[:value_method] || :id, options[:label_method] || :name,
                                        { checked: selected },
                                        data: {
                                          action: "change->filter-counter#updateCount",
                                          filter_counter_target: "checkbox",
                                          filter_typeahead_target: "item",
                                        } do |b| %>
          <div class="flex items-center group hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md px-2 py-1 transition-colors duration-150">
            <%= b.check_box class: "h-4 w-4 rounded border-gray-300 dark:border-gray-600 text-primary-500 focus:ring-primary-500 focus:ring-offset-white dark:focus:ring-offset-gray-800 bg-white dark:bg-gray-700" %>
            <%= b.label class: "ml-3 text-sm font-medium text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-gray-200" %>
          </div>
        <% end %>
      </div>

      <div class="pt-3 border-t border-gray-700">
        <% if options[:display_spinner] %>
          <%= form.submit "Apply",
                          data: { loading_target: "button" },
                          class: "w-full px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-gray-800 transition-colors duration-150" %>

          <!-- Spinner (hidden by default) -->
          <div data-loading-target="spinner" class="hidden w-full px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-md opacity-50 cursor-not-allowed">
            <div class="flex items-center justify-center">
              <%= spinner color: :white %>
            </div>
          </div>
        <% else %>
          <%= form.submit "Apply",
                          class: "w-full px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-gray-800 transition-colors duration-150" %>
        <% end %>
      </div>
    </div>
  </div>
</div>
