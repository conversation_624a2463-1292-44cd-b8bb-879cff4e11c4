<% content_for :title, "Daily Campaign Spends" %>

<div class="w-full h-screen flex flex-col">
  <% if notice.present? %>
    <%= alerting :info, notice %>
  <% end %>

  <header class="border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 shadow-sm">
    <div class="max-w-8xl px-4 h-16 flex items-center sm:px-6 2xl:px-12 mx-auto">
      <div class="flex flex-col md:flex-row md:items-center md:justify-between w-full">
        <div class="flex items-end space-x-2">
          <%= image_tag "feedmob-logo-full-white-1000px.png", class: "mx-auto h-8 w-auto hidden dark:block", alt: "feedmob logo" %>
          <%= image_tag "feedmob-logo-full-white-1000px.png", class: "mx-auto h-8 w-auto block dark:hidden", alt: "feedmob logo" %>
          <span class="inline-block border border-primary-500 dark:border-primary-700 rounded-md px-1.5 py-0.5 text-xs font-medium text-primary-700 dark:text-primary-300 tracking-wide">
            daily spends
          </span>
        </div>

        <div class="mt-4 md:mt-0 md:ml-4 flex flex-col md:flex-row gap-2 w-full md:w-auto">
          <div class="flex items-center space-x-4">
            <%= link_to monthly_campaign_spends_path(to_monthly_spend_params(request.query_parameters)),
                        class: "inline-flex items-center gap-x-2 rounded-full bg-primary-100 dark:bg-primary-900 px-4 py-2 text-sm font-medium text-primary-700 dark:text-primary-300 hover:bg-primary-200 dark:hover:bg-primary-800 transition-all duration-200 ease-in-out shadow-sm hover:shadow-md" do %>
              <%= icon "arrows-right-left", class: "h-4 w-4" %>
              <span>Switch to Monthly View</span>
            <% end %>
            <%= link_to new_workspace_path,
                        class: "inline-flex items-center gap-x-2 rounded-full bg-gradient-to-r from-fuchsia-600 to-blue-600 px-4 py-2 text-sm font-medium text-white hover:from-fuchsia-500 hover:to-blue-500 transition-all duration-200 ease-in-out shadow-sm hover:shadow-md" do %>
              <%= icon "bolt", class: "h-4 w-4 text-yellow-300" %>
              <span>Power Mode</span>
            <% end %>
            <%= render "task_run" %>
            <%= render "export_aggregated" %>
            <%= render "refresh" %>
          </div>
        </div>
      </div>
    </div>
  </header>

  <div class="grow overflow-y-scroll">
    <%= turbo_frame_tag "campaign_spend_stats", src: stats_campaign_spends_path(request.query_parameters) do %>
      <div class="bg-gray-50 dark:bg-gray-950 shadow mb-4">
        <div class="mx-auto max-w-8xl">
          <div class="px-4 py-3 sm:px-6 2xl:px-12">
            <div class="relative overflow-hidden rounded-lg bg-white dark:bg-gray-800 px-4 py-3">
              <div class="flex justify-between items-start">
                <div>
                  <dt class="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Total Spend
                  </dt>
                  <div class="flex gap-4 mt-1">
                    <dd class="h-8 w-32 rounded bg-gray-200 dark:bg-gray-700 animate-pulse"></dd>
                    <dd class="h-8 w-32 rounded bg-gray-200 dark:bg-gray-700 animate-pulse"></dd>
                  </div>
                </div>

                <div class="text-right">
                  <dt class="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Margin
                  </dt>
                  <dd class="mt-1 h-8 w-24 ml-auto rounded bg-gray-200 dark:bg-gray-700 animate-pulse"></dd>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    <% end %>

    <%= turbo_frame_tag "campaign_spend_trend", src: trend_campaign_spends_path(request.query_parameters) do %>
      <div class="bg-gray-50 dark:bg-gray-950 shadow">
        <div class="mx-auto max-w-8xl">
          <div class="px-4 pt-3 sm:px-6 2xl:px-12">
            <div class="relative overflow-hidden rounded-lg bg-white dark:bg-gray-800">
              <div class="h-[400px] animate-pulse bg-gray-200 dark:bg-gray-700"></div>
            </div>
          </div>
        </div>
      </div>
    <% end %>

    <%= render "filters" %>

    <div class="mx-auto max-w-8xl px-4 sm:px-6 2xl:px-12">
      <div class="flex-1 min-h-0">
        <% if @campaign_spends.any? %>
          <div class="flow-root">
            <div class="relative">
              <table class="min-w-full">
                <thead class="bg-gray-100 dark:bg-gray-950">
                  <tr>
                    <th scope="col" class="px-2 py-2 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 sm:pl-3 whitespace-nowrap">
                      <div class="flex items-center gap-x-1">
                        <%= icon "calendar", class: "h-4 w-4 text-primary-600 dark:text-primary-400" %>
                        <span>Date</span>
                      </div>
                    </th>

                    <th scope="col" class="px-2 py-2 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 whitespace-nowrap">
                      <div class="flex items-center gap-x-1">
                        <%= icon "globe-alt", class: "h-4 w-4 text-primary-600 dark:text-primary-400" %>
                      </div>
                    </th>

                    <th scope="col" class="px-2 py-2 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 whitespace-nowrap">
                      <div class="flex items-center gap-x-1">
                        <span>Campaign</span>
                      </div>
                    </th>
                    <th scope="col" class="px-2 py-2 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 whitespace-nowrap">
                      <div class="flex items-center gap-x-1">
                        <%= icon "building-storefront", class: "h-4 w-4 text-primary-600 dark:text-primary-400" %>
                        <span>Vendor</span>
                      </div>
                    </th>
                    <th scope="col" class="px-2 py-2 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 whitespace-nowrap">
                      <div class="flex items-center gap-x-1">
                        <%= icon "eye", class: "h-4 w-4 text-primary-600 dark:text-primary-400" %>
                        <span>Impressions</span>
                      </div>
                    </th>
                    <th scope="col" class="px-2 py-2 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 whitespace-nowrap">
                      <div class="flex items-center gap-x-1">
                        <%= icon "cursor-arrow-ripple", class: "h-4 w-4 text-primary-600 dark:text-primary-400" %>
                        <span>Clicks</span>
                      </div>
                    </th>
                    <th scope="col" class="px-2 py-2 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 whitespace-nowrap">
                      <div class="flex items-center gap-x-1">
                        <%= icon "arrow-down-circle", class: "h-4 w-4 text-primary-600 dark:text-primary-400" %>
                        <span>Installs</span>
                      </div>
                    </th>
                    <th scope="col" class="px-2 py-2 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 whitespace-nowrap">
                      <div class="flex items-center gap-x-1">
                        <%= icon "chart-bar-square", class: "h-4 w-4 text-primary-600 dark:text-primary-400" %>
                        <span>CVR</span>
                      </div>
                    </th>
                    <th scope="col" class="px-2 py-2 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 whitespace-nowrap">
                      <div class="flex items-center gap-x-1">
                        <%= icon "currency-dollar", class: "h-4 w-4 text-primary-600 dark:text-primary-400" %>
                        <span>Gross Spend</span>
                      </div>
                    </th>
                    <th scope="col" class="px-2 py-2 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 whitespace-nowrap">
                      <div class="flex items-center gap-x-1">
                        <%= icon "currency-dollar", class: "h-4 w-4 text-primary-600 dark:text-primary-400" %>
                        <span>Net Spend</span>
                      </div>
                    </th>
                    <th scope="col" class="px-2 py-2 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 whitespace-nowrap">
                      <div class="flex items-center gap-x-1">
                        <%= icon "banknotes", class: "h-4 w-4 text-primary-600 dark:text-primary-400" %>
                        <span>Revenue</span>
                      </div>
                    </th>
                    <th scope="col" class="px-2 py-2 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 whitespace-nowrap">
                      <div class="flex items-center gap-x-1">
                        <%= icon "chart-bar", class: "h-4 w-4 text-primary-600 dark:text-primary-400" %>
                        <span>Margin</span>
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-950">
                  <% @campaign_spends.each do |spend| %>
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200 ease-in-out">
                      <td class="px-2 py-2 text-sm font-medium text-gray-900 dark:text-gray-100 whitespace-nowrap">
                        <%= spend.spend_date.strftime("%Y-%m-%d") %>
                      </td>

                      <td class="px-2 py-2 text-sm text-gray-900 dark:text-gray-100 whitespace-nowrap">
                        <span class="text-lg"><%= CountryCode.emoji_flag(spend.campaign.country_code) %></span>
                      </td>

                      <td class="px-2 py-2 text-sm text-gray-900 dark:text-gray-100 whitespace-nowrap">
                        <%= link_to legacy_url("campaigns", spend.legacy_campaign_id), target: "_blank" do %>
                          <span class="font-medium hover:underline"><%= spend.campaign.name %></span>
                        <% end %>

                        <span class="text-xs">
                          <%= link_to legacy_url("click_urls", spend.legacy_click_url_id), target: "_blank" do %>
                            <span class="font-mono hover:underline">(<%= spend.legacy_click_url_id %>)</span>
                          <% end %>
                        </span>
                      </td>

                      <td class="px-2 py-2 text-sm text-gray-900 dark:text-gray-100 whitespace-nowrap">
                        <%= link_to legacy_url("vendors", spend.legacy_partner_id), target: "_blank" do %>
                          <span class="font-medium hover:underline"><%= spend.partner.name %></span>
                        <% end %>
                      </td>

                      <td class="px-2 py-2 text-sm text-gray-900 dark:text-gray-100 whitespace-nowrap">
                        <% if spend.impression_count.present? %>
                          <%= number_with_delimiter(spend.impression_count) %>
                        <% else %>
                          <span class="text-gray-500 dark:text-gray-400">--</span>
                        <% end %>
                      </td>

                      <td class="px-2 py-2 text-sm text-gray-900 dark:text-gray-100 whitespace-nowrap">
                        <% if spend.click_count.present? %>
                          <%= number_with_delimiter(spend.click_count) %>
                        <% else %>
                          <span class="text-gray-500 dark:text-gray-400">--</span>
                        <% end %>
                      </td>

                      <td class="px-2 py-2 text-sm text-gray-900 dark:text-gray-100 whitespace-nowrap">
                        <% if spend.install_count.present? %>
                          <%= number_with_delimiter(spend.install_count) %>
                        <% else %>
                          <span class="text-gray-500 dark:text-gray-400">--</span>
                        <% end %>
                      </td>

                      <td class="px-2 py-2 text-sm text-gray-900 dark:text-gray-100 whitespace-nowrap">
                        <% if spend.cvr.present? %>
                          <span class="font-medium">
                            <%= number_to_percentage(spend.cvr, precision: 2) %>
                          </span>
                        <% else %>
                          <span class="text-gray-500 dark:text-gray-400">--</span>
                        <% end %>
                      </td>

                      <td class="px-2 py-2 text-sm text-gray-900 dark:text-gray-100 whitespace-nowrap">
                        <% if spend.gross_spend_source == "event_aggregate/#{EventDataOrigin::FEEDMOB}" %>
                          <%= link_to conversion_records_link(
                                spend.spend_date,
                                spend.spend_date,
                                spend.legacy_click_url_id,
                                spend.gross_campaign_spend.calculation_metadata["event"]
                              ), target: "_blank" do %>
                            <%= tooltip(
                                  format_spend_amount(spend.gross_spend),
                                  spend_tooltip_content(spend.gross_campaign_spend),
                                  { position: :top }
                                ) %>
                          <% end %>
                        <% else %>
                          <%= tooltip(
                                format_spend_amount(spend.gross_spend),
                                spend_tooltip_content(spend.gross_campaign_spend),
                                { position: :top }
                              ) %>
                        <% end %>
                      </td>

                      <!-- For the Net Spend column -->
                      <td class="px-2 py-2 text-sm text-gray-900 dark:text-gray-100 whitespace-nowrap">
                        <% if spend.net_spend_source == "event_aggregate/#{EventDataOrigin::FEEDMOB}" %>
                          <%= link_to conversion_records_link(
                                spend.spend_date,
                                spend.spend_date,
                                spend.legacy_click_url_id,
                                spend.gross_campaign_spend.calculation_metadata["event"]
                              ), target: "_blank" do %>
                            <div class="flex items-center gap-x-1">
                              <%= tooltip(
                                    format_spend_amount(spend.adjusted_net_spend),
                                    spend_tooltip_content(spend.net_campaign_spend),
                                    { position: :top }
                                  ) %>
                              <% if spend.net_spend_adjustment.present? %>
                                <%= icon("arrow-down-circle", class: "h-4 w-4 text-gray-500") %>
                              <% end %>
                            </div>
                          <% end %>
                        <% else %>
                          <div class="flex items-center gap-x-1">
                            <%= tooltip(
                                  format_spend_amount(spend.adjusted_net_spend, spend.net_campaign_spend),
                                  spend_tooltip_content(spend.net_campaign_spend),
                                  { position: :top }
                                ) %>
                            <% if spend.net_spend_adjustment.present? %>
                              <%= icon("arrow-down-circle", class: "h-4 w-4 text-gray-500") %>
                            <% end %>
                          </div>
                        <% end %>
                      </td>

                      <td class="px-2 py-2 text-sm whitespace-nowrap">
                        <% revenue_color = case
                             when spend.revenue > 0 then "text-green-400"
                             when spend.revenue < 0 then "text-red-400"
                             else "text-gray-400"
                             end %>
                        <span class="font-medium <%= revenue_color %>">
                          <%= format_spend_amount(spend.revenue) %>
                        </span>
                      </td>

                      <td class="px-2 py-2 text-sm whitespace-nowrap">
                        <% if spend.adjusted_margin.nil? %>
                          <span class="text-gray-500">--</span>
                        <% else %>
                          <% margin_color = case
                               when spend.adjusted_margin > 0 then "text-green-400"
                               when spend.adjusted_margin < 0 then "text-red-400"
                               else "text-gray-400"
                               end %>
                          <span class="font-medium <%= margin_color %>">
                            <%= number_to_percentage(spend.adjusted_margin, precision: 0) %>
                          </span>
                        <% end %>
                      </td>
                    </tr>
                  <% end %>
                </tbody>


              </table>
            </div>
          </div>
        <% else %>
          <div class="flex flex-col items-center justify-center min-h-[400px] text-center">
            <div class="rounded-full bg-gray-800/50 p-6 mb-4">
              <%= icon "currency-dollar", class: "h-12 w-12 text-primary-400" %>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">No campaign spends found</h3>
            <p class="mt-2 text-sm text-gray-600 dark:text-gray-400 max-w-sm">
              Campaign spends will appear here once available.
            </p>
          </div>
        <% end %>
      </div>

      <div class="py-4 bg-gray-950">
        <%== pagy_nav(@pagy) if @pagy.pages > 1 %>
      </div>
    </div>
  </div>
</div>
