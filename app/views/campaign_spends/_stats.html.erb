<%= turbo_frame_tag "campaign_spend_stats" do %>
  <div class="bg-gray-50 dark:bg-gray-950 shadow">
    <div class="mx-auto max-w-8xl">
      <div class="px-4 pt-3 sm:px-6 2xl:px-12">
        <div class="relative overflow-hidden rounded-lg bg-white dark:bg-gray-900 px-4 py-3">
          <!-- Date Range Header -->
          <div class="mb-3 pb-2 border-b border-gray-200 dark:border-gray-700">
            <div data-controller="dropdown" class="relative" style="position: relative; z-index: 20;">
              <button type="button"
                      data-action="dropdown#toggle"
                      class="inline-flex items-center gap-3 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-md px-3 py-2 transition-colors duration-150 cursor-pointer border border-gray-300 dark:border-gray-700">
                <div class="flex items-center gap-3">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2v12a2 2 0 002 2z" />
                  </svg>
                  <div class="flex items-center gap-2">
                    <span class="text-sm font-semibold text-gray-900 dark:text-gray-100">
                      <%= @start_date.strftime("%B %d, %Y") %>
                    </span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-600 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7" />
                    </svg>
                    <span class="text-sm font-semibold text-gray-900 dark:text-gray-100">
                      <%= @end_date.strftime("%B %d, %Y") %>
                    </span>
                  </div>
                  <% if (@end_date - @start_date).to_i > 0 %>
                    <span class="text-xs text-gray-600 dark:text-gray-400 ml-1">
                      (<%= pluralize((@end_date - @start_date).to_i + 1, "day") %>)
                    </span>
                  <% end %>
                  <%= icon "chevron-down", set: "mini", class: "h-4 w-4 text-gray-600 dark:text-gray-300 ml-1" %>
                </div>
              </button>

              <div data-dropdown-target="menu"
                data-controller="date-range"
                data-date-range-sync-target-value="#filter-form"
                class="hidden fixed left-50 z-50 mt-2 w-64 origin-top-right rounded-md bg-white dark:bg-gray-800 p-4 shadow-2xl ring-1 ring-gray-200 dark:ring-gray-700 border border-gray-300 dark:border-gray-600"
                style="position: fixed; z-index: 9999;">
                <div class="space-y-3">
                  <div>
                    <label class="block text-sm font-medium text-gray-200">Start Date</label>
                    <input type="date"
                           value="<%= @start_date&.strftime("%Y-%m-%d") %>"
                           data-date-range-target="startDate"
                           class="mt-1 block w-full rounded-md border-0 bg-gray-700 px-3 py-2 text-gray-100 ring-1 ring-inset ring-gray-600 focus:ring-2 focus:ring-primary-500 text-sm" />
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-200">End Date</label>
                    <input type="date"
                           value="<%= @end_date&.strftime("%Y-%m-%d") %>"
                           data-date-range-target="endDate"
                           class="mt-1 block w-full rounded-md border-0 bg-gray-700 px-3 py-2 text-gray-100 ring-1 ring-inset ring-gray-600 focus:ring-2 focus:ring-primary-500 text-sm" />
                  </div>
                  <%= render "campaign_spends/quick_select_buttons" %>
                </div>
              </div>
            </div>
          </div>

          <div class="flex justify-between items-start">
            <div>
              <dt class="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Spend
                <span class="text-xs italic">(incl. overcap release & excl. client make good)</span>
              </dt>
              <div class="flex gap-4 mt-1">
                <dd class="text-2xl font-semibold tracking-tight text-gray-900 dark:text-gray-100">
                  <% client_parts = number_to_currency(@client_spend).split(".") %>
                  <%= client_parts[0] %><span class="text-sm font-normal text-gray-500 dark:text-gray-400">.<%= client_parts[1] %></span>
                  <span class="text-sm font-normal text-gray-500 dark:text-gray-400">client</span>
                </dd>

                <div>
                  <dd class="text-2xl font-semibold tracking-tight text-gray-900 dark:text-gray-100">
                    <% partner_parts = number_to_currency(@partner_spend).split(".") %>
                    <%= partner_parts[0] %><span class="text-sm font-normal text-gray-500 dark:text-gray-400">.<%= partner_parts[1] %></span>
                    <span class="text-sm font-normal text-gray-500 dark:text-gray-400">vendor</span>
                    <% if @adjusted_net_spend != 0 %>
                      <%= tooltip(
                            content_tag(:span, icon("question-mark-circle", class: "h-4 w-4 inline-block ml-0.5 mt-1 text-gray-500 dark:text-gray-400 cursor-help"), class: "group relative inline-block"),
                            "Total net spend has been reduced by Overcap Release.",
                            {
                              position: :right,
                              trigger_classes: "group relative inline-flex items-center hover:cursor-help",
                              distance: 8,
                            }
                          ) %>
                    <% end %>
                  </dd>
                  <% if @adjusted_net_spend != 0 %>
                    <div class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                      <span class="font-medium">Overcap Release:</span>
                      <% adjustment_parts = number_to_currency(@adjusted_net_spend.abs).split(".") %>
                      <%= adjustment_parts[0] %><span>.<%= adjustment_parts[1] %></span>
                    </div>
                  <% end %>
                </div>
              </div>
            </div>

            <% if @client_spend && @client_spend > 0 %>
              <% margin = (@client_spend - @partner_spend) / @client_spend.to_f %>
              <div class="flex text-right space-x-4">
                <div>
                  <dt class="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Net Revenue
                  </dt>
                  <dd class="mt-1 text-2xl font-semibold tracking-tight text-gray-900 dark:text-gray-100">
                    <% net_revenue_parts = number_to_currency(@revenue).split(".") %>
                    <%= net_revenue_parts[0] %><span class="text-sm font-normal text-gray-500 dark:text-gray-400">.<%= net_revenue_parts[1] %></span>
                  </dd>
                </div>

                <div>
                  <dt class="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Margin
                  </dt>
                  <dd class="mt-1 text-2xl font-semibold tracking-tight
                    <%= if margin < 0
                          "text-red-400"
                        elsif margin < 0.2
                          "text-yellow-400"
                        else
                          "text-green-400"
                        end %>">
                    <% percentage_parts = number_to_percentage(margin * 100, precision: 1).split(".") %>
                    <%= percentage_parts[0] %><span class="text-sm font-normal opacity-70">.<%= percentage_parts[1] %></span>
                  </dd>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
<% end %>
