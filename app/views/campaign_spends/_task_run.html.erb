<div class="flex gap-4">
  <%# First Stat - Last Check %>
  <div class="flex items-center">
    <% if @latest_task_run %>
      <%= link_to tasks_path(task_id: @latest_task_run&.task_definition_id, run_id: @latest_task_run&.id), target: "_blank" do %>
        <div class="inline-flex items-center">
          <span class="mr-0.5"><%= task_status_icon(@latest_task_run.status) %></span>
          <%= tooltip(
            content_tag(:div, class: "text-xs") do %>
              <span class="text-gray-600 dark:text-gray-400">Last Check:</span>
              <span class="text-gray-700 dark:text-gray-300">
                <%= time_ago_in_words(@latest_task_run.created_at) %> ago
              </span>
            <% end,
            "Checked at: #{@latest_task_run.created_at.strftime("%Y-%m-%d %H:%M UTC")} (Click to view details)",
            { position: :bottom }
          ) %>
        </div>
      <% end %>
    <% end %>
  </div>

  <%# Second Stat - Last Refresh %>
  <div class="flex items-center">
    <% if @latest_refresh_task_run %>
      <%= link_to tasks_path(task_id: @latest_refresh_task_run&.task_definition_id, run_id: @latest_refresh_task_run&.id), target: "_blank" do %>
        <div class="inline-flex items-center">
          <span class="mr-0.5"><%= task_status_icon(@latest_refresh_task_run.status) %></span>
          <%= tooltip(
            content_tag(:div, class: "text-xs") do %>
              <span class="text-gray-600 dark:text-gray-400">Last Refresh:</span>
              <span class="text-gray-700 dark:text-gray-300">
                <%= time_ago_in_words(@latest_refresh_task_run.created_at) %> ago
              </span>
            <% end,
            "Next refresh: #{(Date.tomorrow.to_time.utc + 2.hours + 10.minutes).strftime("%Y-%m-%d %H:%M UTC")} (Click to view details)",
            { position: :bottom }
          ) %>
        </div>
      <% end %>
    <% end %>
  </div>
</div>
