<%= turbo_frame_tag "campaign_spend_stats" do %>
  <div class="bg-gray-50 dark:bg-gray-950 shadow rounded-lg">
    <div class="mx-auto max-w-8xl">
      <div class="px-4 pt-3 sm:px-6 2xl:px-12">
        <div class="relative overflow-hidden rounded-lg bg-white dark:bg-gray-900 px-4 py-3">
          <!-- Enhanced Date Range Header -->
          <div class="mb-3 pb-2 border-b border-gray-700">
            <div data-controller="dropdown month-range" class="relative" style="position: relative; z-index: 20;">
              <button type="button"
                      data-action="dropdown#toggle"
                      class="inline-flex items-center gap-3 bg-gray-800 hover:bg-gray-700 rounded-md px-3 py-2 transition-colors duration-150 cursor-pointer border border-gray-700">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <div class="flex items-center gap-1">
                  <% if @start_month == @end_month %>
                    <span class="text-sm font-semibold text-white">
                      <%= @start_month.strftime("%B %Y") %>
                    </span>
                  <% else %>
                    <div class="flex items-center gap-2">
                      <span class="text-sm font-semibold text-white">
                        <%= @start_month.strftime("%B %Y") %>
                      </span>
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7" />
                      </svg>
                      <span class="text-sm font-semibold text-white">
                        <% if @end_month == Date.current.beginning_of_month %>
                          <%= @end_month.strftime("%B %Y") %> <span class="text-xs font-normal text-gray-300 ml-1">(up to <%= (Date.current - 1.day).strftime("%d %b") %>)</span>
                        <% else %>
                          <%= @end_month.strftime("%B %Y") %>
                        <% end %>
                      </span>
                    </div>
                  <% end %>
                  <%= icon "chevron-down", set: "mini", class: "h-4 w-4 text-gray-300 ml-1" %>
                </div>
              </button>

              <div data-dropdown-target="menu"
                  class="hidden fixed left-50 z-50 mt-2 w-64 origin-top-right rounded-md bg-gray-800 p-4 shadow-2xl ring-1 ring-gray-700 border border-gray-600"
                  style="position: fixed; z-index: 9999;">
                <div class="space-y-3">
                  <div>
                    <label class="block text-sm font-medium text-gray-200">Start Month</label>
                    <input type="month"
                           data-month-range-target="startMonth"
                           value="<%= @start_month&.strftime("%Y-%m") %>"
                           class="mt-1 block w-full rounded-md border-0 bg-gray-700 px-3 py-2 text-gray-100 ring-1 ring-inset ring-gray-600 focus:ring-2 focus:ring-primary-500 text-sm" />
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-200">End Month</label>
                    <input type="month"
                           data-month-range-target="endMonth"
                           value="<%= @end_month&.strftime("%Y-%m") %>"
                           class="mt-1 block w-full rounded-md border-0 bg-gray-700 px-3 py-2 text-gray-100 ring-1 ring-inset ring-gray-600 focus:ring-2 focus:ring-primary-500 text-sm" />
                  </div>
                  <%= render "monthly_campaign_spends/quick_select_buttons" %>
                </div>
              </div>
            </div>
          </div>

          <div class="flex justify-between items-start">
            <div>
              <dt class="text-sm font-medium text-gray-400">
                Total Spend
                <span class="text-xs italic">(incl. client make good & overcap release)</span>
              </dt>
              <div class="flex gap-4 mt-1">
                <div>
                  <dd class="text-2xl font-semibold tracking-tight text-gray-100">
                    <% client_parts = number_to_currency(@client_spend).split(".") %>
                    <%= client_parts[0] %><span class="text-sm font-normal text-gray-400">.<%= client_parts[1] %></span>
                    <span class="text-sm font-normal text-gray-400">client</span>
                  </dd>
                  <% if @monthly_gross_adjustment != 0 %>
                    <div class="text-sm text-gray-400 mt-1">
                      <span class="font-medium">Client Make Good:</span>
                      <% gross_adj_parts = number_to_currency(@monthly_gross_adjustment).split(".") %>
                      <%= gross_adj_parts[0] %><span>.<%= gross_adj_parts[1] %></span>
                    </div>
                  <% end %>
                </div>

                <div>
                  <dd class="text-2xl font-semibold tracking-tight text-gray-100">
                    <% partner_parts = number_to_currency(@partner_spend).split(".") %>
                    <%= partner_parts[0] %><span class="text-sm font-normal text-gray-400">.<%= partner_parts[1] %></span>
                    <span class="text-sm font-normal text-gray-400">vendor</span>
                      <% if @adjusted_net_spend != 0 || @monthly_net_adjustment != 0 %>
                        <%= tooltip(
                              content_tag(:span, icon("question-mark-circle", class: "h-4 w-4 inline-block ml-0.5 mt-1 text-gray-400 cursor-help"), class: "group relative inline-block"),
                              "Total net spend has been reduced by Overcap Release and Make Good (Net).",
                              {
                                position: :right,
                                trigger_classes: "group relative inline-flex items-center hover:cursor-help",
                                distance: 8,
                              }
                            ) %>
                      <% end %>
                  </dd>
                  <% if @adjusted_net_spend != 0 %>
                    <div class="text-sm text-gray-400 mt-1">
                      <span class="font-medium">Overcap Release:</span>
                      <% adjustment_parts = number_to_currency(@adjusted_net_spend.abs).split(".") %>
                      <%= adjustment_parts[0] %><span>.<%= adjustment_parts[1] %></span>
                    </div>
                  <% end %>
                  <% if @monthly_net_adjustment != 0 %>
                    <div class="text-sm text-gray-400 mt-1">
                      <span class="font-medium">Make Good (Net):</span>
                      <% net_adj_parts = number_to_currency(@monthly_net_adjustment.abs).split(".") %>
                      <%= net_adj_parts[0] %><span>.<%= net_adj_parts[1] %></span>
                    </div>
                  <% end %>
                </div>
              </div>
            </div>

            <% if @client_spend && @client_spend > 0 %>
              <div class="flex text-right space-x-4">
                <div>
                  <dt class="text-sm font-medium text-gray-400">
                    Net Revenue
                  </dt>
                  <dd class="mt-1 text-2xl font-semibold tracking-tight text-gray-100">
                    <% net_revenue_parts = number_to_currency(@revenue).split(".") %>
                    <%= net_revenue_parts[0] %><span class="text-sm font-normal text-gray-400">.<%= net_revenue_parts[1] %></span>
                  </dd>
                </div>

                <div>
                  <dt class="text-sm font-medium text-gray-400">
                    Margin
                  </dt>
                  <dd class="mt-1 text-2xl font-semibold tracking-tight
                    <%= if @margin < 0
                          "text-red-400"
                        elsif @margin < 0.2
                          "text-yellow-400"
                        else
                          "text-green-400"
                        end %>">
                    <% percentage_parts = number_to_percentage(@margin * 100, precision: 1).split(".") %>
                    <%= percentage_parts[0] %><span class="text-sm font-normal opacity-70">.<%= percentage_parts[1] %></span>
                  </dd>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
<% end %>
