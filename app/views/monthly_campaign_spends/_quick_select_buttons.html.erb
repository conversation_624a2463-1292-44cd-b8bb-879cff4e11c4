<!-- Quick Select Buttons -->
  <div class="grid grid-cols-2 gap-2">
    <button type="button"
            data-action="month-range#setLastMonth"
            class="px-3 py-1 text-xs font-medium text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-700 rounded hover:bg-gray-300 dark:hover:bg-gray-600">
      Last Month
    </button>
    <button type="button"
            data-action="month-range#setLast3Months"
            class="px-3 py-1 text-xs font-medium text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-700 rounded hover:bg-gray-300 dark:hover:bg-gray-600">
      Last 3 Months
    </button>
    <button type="button"
            data-action="month-range#setLast6Months"
            class="px-3 py-1 text-xs font-medium text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-700 rounded hover:bg-gray-300 dark:hover:bg-gray-600">
      Last 6 Months
    </button>
    <button type="button"
            data-action="month-range#setLastYear"
            class="px-3 py-1 text-xs font-medium text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-700 rounded hover:bg-gray-300 dark:hover:bg-gray-600">
      Last Year
    </button>
  </div>

   <!-- Add Apply button -->
  <div class="pt-3 border-t border-gray-700">
    <button type="button"
            data-action="month-range#syncToMainForm"
            class="w-full px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700 transition-colors">
      Apply
    </button>
  </div>

