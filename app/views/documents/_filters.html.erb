<div class="bg-white dark:bg-gray-900 py-4 border-b border-gray-200 dark:border-gray-800">
  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <section aria-labelledby="filter-heading">
      <%= form_with model: @query, scope: :query, url: documents_path, method: :get, data: { controller: "auto-submit" } do |form| %>
        <div class="flex items-center justify-between">
          <!-- Left side filters -->
          <div class="flex items-center space-x-3">
            <!-- Sort Dropdown -->
            <div data-controller="dropdown" class="relative">
              <button type="button"
                      data-action="dropdown#toggle"
                      class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 bg-gray-100 dark:bg-gray-800 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-150">
                Sort
                <%= icon "chevron-down", set: "mini", class: "-mr-1 ml-1.5 h-5 w-5 text-gray-400" %>
              </button>

              <div data-dropdown-target="menu"
                   data-transition-enter-from="opacity-0 scale-95"
                   data-transition-enter-to="opacity-100 scale-100"
                   data-transition-leave-from="opacity-100 scale-100"
                   data-transition-leave-to="opacity-0 scale-95"
                   class="hidden absolute right-0 z-20 mt-2 w-40 origin-top-right rounded-md bg-white dark:bg-gray-800 shadow-lg ring-1 ring-gray-200 dark:ring-gray-700 focus:outline-none">
                <div class="py-1">
                  <%= form.collection_radio_buttons :sort_by, [["date_asc", "Oldest"], ["date_desc", "Newest"]],
                                                    :first, :last, {}, data: { action: "auto-submit#submit" } do |b| %>
                    <label class="flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer">
                      <%= b.radio_button class: "hidden peer" %>
                      <span class="text-sm font-medium text-gray-700 dark:text-gray-200 peer-checked:text-primary-600 dark:peer-checked:text-primary-400"><%= b.text %></span>
                      <%= icon "check", class: "ml-auto h-5 w-5 text-primary-600 dark:text-primary-400 hidden peer-checked:block" %>
                    </label>
                  <% end %>
                </div>
              </div>
            </div>

            <!-- Limit Dropdown -->
            <div data-controller="dropdown" class="relative">
              <button type="button"
                      data-action="click->dropdown#toggle"
                      class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 bg-gray-100 dark:bg-gray-800 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-150">
                Limit
                <%= icon "chevron-down", set: "mini", class: "-mr-1 ml-1.5 h-5 w-5 text-gray-400" %>
              </button>

              <div data-dropdown-target="menu"
                  class="hidden absolute right-0 z-20 mt-2 w-40 origin-top-right rounded-md bg-white dark:bg-gray-800 shadow-lg ring-1 ring-gray-200 dark:ring-gray-700 focus:outline-none">
                <div class="py-1">
                  <%= form.collection_radio_buttons :limit, [[20, "20"], [100, "100"]],
                                                    :first, :last, {}, data: { action: "auto-submit#submit" } do |b| %>
                    <label class="flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer">
                      <%= b.radio_button class: "hidden peer" %>
                      <span class="text-sm font-medium text-gray-700 dark:text-gray-200 peer-checked:text-primary-600 dark:peer-checked:text-primary-400"><%= b.text %></span>
                      <%= icon "check", class: "ml-auto h-5 w-5 text-primary-600 dark:text-primary-400 hidden peer-checked:block" %>
                    </label>
                  <% end %>
                </div>
              </div>
            </div>
          </div>

          <!-- Right side filters -->
          <div class="flex items-center space-x-3">
            <!-- Enhanced Search -->
            <div data-controller="search-expand" class="relative">
              <div class="flex items-center">
                <%= form.text_field :search,
                                    placeholder: "Search by title and summary",
                                    data: {
                                      search_expand_target: "input",
                                      action: "focus->search-expand#expand blur->search-expand#collapse",
                                    },
                                    class: "w-40 focus:w-64 transition-all duration-300 ease-in-out rounded-md border-0 bg-gray-100 dark:bg-gray-700 px-3 py-2 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 ring-1 ring-inset ring-gray-300 dark:ring-gray-600 focus:ring-2 focus:ring-primary-500 text-sm pr-10" %>
                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <%= icon "magnifying-glass", set: "mini", class: "h-5 w-5 text-gray-600 dark:text-gray-400" %>
                </div>
              </div>
            </div>

            <!-- Date Range -->
            <div data-controller="dropdown" class="relative">
              <button type="button"
                      data-action="dropdown#toggle"
                      class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 bg-gray-100 dark:bg-gray-800 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-150">
                Date range
                <%= icon "calendar", set: "mini", class: "-mr-1 ml-1.5 h-5 w-5 text-gray-600 dark:text-gray-400" %>
              </button>

              <div data-dropdown-target="menu"
                   class="hidden absolute right-0 z-20 mt-2 w-64 origin-top-right rounded-md bg-white dark:bg-gray-800 p-4 shadow-lg ring-1 ring-gray-200 dark:ring-gray-700">
                <div class="space-y-3">
                  <div>
                    <%= form.label :date_gteq, "Start Date", class: "block text-sm font-medium text-gray-700 dark:text-gray-200" %>
                    <%= form.date_field :date_gteq,
                                        data: {},
                                        class: "mt-1 block w-full rounded-md border-0 bg-gray-100 dark:bg-gray-700 px-3 py-2 text-gray-900 dark:text-gray-100 ring-1 ring-inset ring-gray-300 dark:ring-gray-600 focus:ring-2 focus:ring-primary-500 text-sm" %>
                  </div>
                  <div>
                    <%= form.label :date_lteq, "End Date", class: "block text-sm font-medium text-gray-700 dark:text-gray-200" %>
                    <%= form.date_field :date_lteq,
                                        data: {},
                                        class: "mt-1 block w-full rounded-md border-0 bg-gray-100 dark:bg-gray-700 px-3 py-2 text-gray-900 dark:text-gray-100 ring-1 ring-inset ring-gray-300 dark:ring-gray-600 focus:ring-2 focus:ring-primary-500 text-sm" %>
                  </div>
                  <div class="mt-4">
                    <%= form.submit "Apply Date Filter",
                                    class: "w-full px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500" %>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <%= form.submit "filter", class: "hidden", id: "filter-submit" %>
      <% end %>
    </section>
  </div>
</div>

