<!DOCTYPE html>
<html class="dark overflow-hidden">
  <head>
    <title><%= content_for(:title) || "FeedMob Assistant" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="turbo-refresh-method" content="morph">
    <meta name="turbo-refresh-scroll" content="preserve">

    <meta property="og:type" content="article">
    <meta property="og:site_name" content="FeedMob Assistant">
    <meta property="og:title" content="<%= content_for?(:og_title) ? yield(:og_title) : 'Your 24/7 AI assistant for daily data insights' %>">
    <meta property="og:url" content="<%= request.original_url %>">
    <meta property="og:image" content="<%= request.base_url %><%= asset_path('femini-slack.png') %>">
    <meta property="og:image:secure_url" content="<%= request.base_url %><%= asset_path('femini-slack.png') %>">
    <meta property="og:image:type" content="image/png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">

    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>

    <!-- PWA Assets -->
    <link rel="manifest" href="/manifest.json">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.svg">
    
    <!-- External Libraries -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tom-select@2.3.1/dist/css/tom-select.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.10.0/styles/github-dark.min.css">
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.10.0/highlight.min.js"></script>

    <!-- Application Styles -->
    <%= stylesheet_link_tag "tailwind", "inter-font", "data-turbo-track": "reload" %>
    <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %>
    <%= stylesheet_link_tag "perspective", "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
  </head>

  <body class="bg-gray-900 text-gray-100 flex h-screen antialiased">
    <% if ["sessions", "passwords"].exclude? controller.controller_name %>
      <%= render "shared/sidebar" if authenticated? %>
    <% end %>

    <main class="w-full h-full overflow-y-auto bg-gray-950">
      <%= render "shared/alerts" if authenticated? %>
      <%= render "shared/heading" %>
      <%= yield %>
    </main>
  </body>
</html>
