import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["darkIcon", "lightIcon"]

  connect() {
    // Check for saved user preference, default to dark mode if not set
    const savedTheme = localStorage.getItem('darkMode')
    const darkMode = savedTheme !== null ? savedTheme === 'true' : true
    this.updateTheme(darkMode)
  }

  toggle() {
    const isDark = document.documentElement.classList.contains('dark')
    this.updateTheme(!isDark)
  }

  updateTheme(darkMode) {
    // Update DOM
    if (darkMode) {
      document.documentElement.classList.add('dark')
      if (this.hasDarkIconTarget) this.darkIconTarget.classList.add('hidden')
      if (this.hasLightIconTarget) this.lightIconTarget.classList.remove('hidden')
    } else {
      document.documentElement.classList.remove('dark')
      if (this.hasDarkIconTarget) this.darkIconTarget.classList.remove('hidden')
      if (this.hasLightIconTarget) this.lightIconTarget.classList.add('hidden')
    }

    // Update color scheme for native elements
    document.documentElement.style.colorScheme = darkMode ? 'dark' : 'light'

    // Save preference
    localStorage.setItem('darkMode', darkMode)
  }
}
