import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["darkIcon", "lightIcon"]

  connect() {
    // Check for saved user preference
    const darkMode = localStorage.getItem('darkMode') === 'true'
    this.updateTheme(darkMode)
  }

  toggle() {
    const isDark = document.documentElement.classList.contains('dark')
    this.updateTheme(!isDark)
  }

  updateTheme(darkMode) {
    // Update DOM
    if (darkMode) {
      document.documentElement.classList.add('dark')
      this.darkIconTarget.classList.add('hidden')
      this.lightIconTarget.classList.remove('hidden')
    } else {
      document.documentElement.classList.remove('dark')
      this.darkIconTarget.classList.remove('hidden')
      this.lightIconTarget.classList.add('hidden')
    }

    // Save preference
    localStorage.setItem('darkMode', darkMode)
  }
}
