import { Controller } from "@hotwired/stimulus"
import "@finos/perspective-viewer"
import "@finos/perspective-viewer-datagrid"
import "@finos/perspective-viewer-d3fc"
import perspective from "@finos/perspective"

export default class extends Controller {
  static values = {
    initialReportId: String,
    initialReportName: String
  }

  static targets = ["loading", "viewer", "placeholder"]

  connect() {
    if (this.hasInitialReportIdValue && this.initialReportIdValue) {
      this.loadReport();
    }
  }

  showLoading() {
    this.loadingTarget.classList.remove('hidden');
  }

  hideLoading() {
    this.loadingTarget.classList.add('hidden');
  }

  async loadReport() {
    const reportId = this.initialReportIdValue;
    const reportName = this.initialReportNameValue;
    this.showLoading();

    try {
      const response = await fetch(`/client_reports/${reportId}.csv`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const csvContent = await response.text();
      const worker = await perspective.worker();
      const table = await worker.table(csvContent);
      await this.viewerTarget.load(table);

      // Determine theme based on current mode
      const isDarkMode = document.documentElement.classList.contains('dark');
      const theme = isDarkMode ? "Pro Dark Enhanced" : "Material Light";

      await this.viewerTarget.restore({
        plugin: "Datagrid",
        settings: true,
        theme: theme,
        title: reportName
      });

    } catch (error) {
      console.error("Error loading data:", error);
    } finally {
      this.hideLoading();
    }
  }
}