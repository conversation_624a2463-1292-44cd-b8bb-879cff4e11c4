/*
 * This is a manifest file that'll be compiled into application.css, which will include all the files
 * listed below.
 *
 * Any CSS (and SCSS, if configured) file within this directory, lib/assets/stylesheets, or any plugin's
 * vendor/assets/stylesheets directory can be referenced here using a relative path.
 *
 * You're free to add application-wide styles to this file and they'll appear at the bottom of the
 * compiled file so the styles you add here take precedence over styles defined in any other CSS
 * files in this directory. Styles in this file should be added after the last require_* statement.
 * It is generally better to create a new file per style scope.
 *
 *= require_tree .
 *= require_self
 */

 :root {
  color-scheme: light;
  --header-height: 65px; /* Adjust based on your actual header height */
  --filters-height: 68px; /* Adjust based on your actual filters height */
  --subheader-height: 161px; /* Adjust based on your actual subheader height */
}

.dark {
  color-scheme: dark;
}


/* Scrollbar styling with dark/light mode support */
:root {
  /* Light mode variables */
  --scrollbar-thumb-light: rgba(0, 0, 0, 0.3);
  --scrollbar-thumb-hover-light: rgba(0, 0, 0, 0.5);
  --scrollbar-track-light: transparent;

  /* Dark mode variables */
  --scrollbar-thumb-dark: rgba(255, 255, 255, 0.3);
  --scrollbar-thumb-hover-dark: rgba(255, 255, 255, 0.5);
  --scrollbar-track-dark: transparent;
}

/* Default scrollbar (light mode) */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: var(--scrollbar-track-light);
}

::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-light);
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover-light);
}

::-webkit-scrollbar-corner {
  background: transparent;
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-track {
  background: var(--scrollbar-track-dark);
}

.dark ::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-dark);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover-dark);
}

/* Firefox scrollbar styling */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb-light) var(--scrollbar-track-light);
}

.dark * {
  scrollbar-color: var(--scrollbar-thumb-dark) var(--scrollbar-track-dark);
}

input {
  color-scheme: light;
}

.dark input {
  color-scheme: dark;
}

@keyframes slideInFromLeft {
  0% {
    transform: scaleX(0);
    transform-origin: left;
    opacity: 0;
  }
  100% {
    transform: scaleX(1);
    transform-origin: left;
    opacity: 1;
  }
}
