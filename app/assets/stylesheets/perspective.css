/* Original styles with modifications marked */

perspective-viewer,
perspective-workspace,
perspective-copy-menu,
perspective-export-menu,
perspective-dropdown,
perspective-date-column-style,
perspective-datetime-column-style,
perspective-number-column-style,
perspective-string-column-style {
    /* --- SVG Icon Variables (Unchanged) --- */
    --column-type-integer--mask-image: url("/svg/number-type.svg");
    --column-type-float--mask-image: var(--column-type-integer--mask-image);
    --column-type-string--mask-image: url("/svg/string-type.svg");
    --column-type-date--mask-image: url("/svg/date-type.svg");
    --column-type-boolean--mask-image: url("/svg/boolean-type.svg");
    --column-type-datetime--mask-image: var(--column-type-date--mask-image);
    --column-drag-handle--mask-image: url("/svg/drag-handle.svg");
    --column-radio-on--mask-image: url("/svg/radio-on.svg");
    --column-radio-hover--mask-image: url("/svg/radio-hover.svg");
    --column-radio-off--mask-image: url("/svg/radio-off.svg");
    --column-checkbox-on--mask-image: url("/svg/checkbox-on.svg");
    --column-checkbox-hover--mask-image: url("/svg/checkbox-hover.svg");
    --column-checkbox-off--mask-image: url("/svg/checkbox-off.svg");
    --column-settings-icon--mask-image: url("/svg/column-settings-icon.svg");
    --sort-asc-icon--mask-image: url("/svg/sort-asc-icon.svg");
    --sort-desc-icon--mask-image: url("/svg/sort-desc-icon.svg");
    --sort-col-asc-icon--mask-image: url("/svg/sort-col-asc-icon.svg");
    --sort-col-desc-icon--mask-image: url("/svg/sort-col-desc-icon.svg");
    --sort-abs-asc-icon--mask-image: url("/svg/sort-abs-asc-icon.svg");
    --sort-abs-desc-icon--mask-image: url("/svg/sort-abs-desc-icon.svg");
    --sort-abs-col-asc-icon--mask-image: url("/svg/sort-abs-col-asc-icon.svg");
    --sort-abs-col-desc-icon--mask-image: url("/svg/sort-abs-col-desc-icon.svg");
    --sort-none-icon--mask-image: url("/svg/sort-none-icon.svg");
    --add-expression-icon--mask-image: url("/svg/expression.svg");
    --close-icon--mask-image: url("/svg/close-icon.svg");
    --inactive-column-selector--content: url("/svg/checkbox-unchecked-icon.svg");
    --active-column-selector--content: url("/svg/checkbox-checked-icon.svg");
    --select-arrow-light--background-image: url("/svg/dropdown-selector-light.svg");
    --select-arrow-dark--background-image: url("/svg/dropdown-selector.svg");
    --overflow-hint-icon--content: "!";
    --reset-button-icon--content: "refresh";
    --save-button-icon--content: "save";
    --updating-icon--mask-image: url("/svg/updating.gif");
    --downloading-icon--mask-image: url("/svg/downloading.gif");
    --status-ok-icon--mask-image: url(/svg/status_ok.svg);
    --export-icon--mask-image: url("/svg/export-icon.svg");
    --free-scroll-icon--mask-image: url("/svg/free-scroll-icon.svg");
    --reset-icon--mask-image: url("/svg/revert-icon.svg");
    --copy-icon--mask-image: url("/svg/duplicate-icon.svg");
    --theme-icon--mask-image: url("/svg/theme-icon.svg");
    --drawer-tab-icon--mask-image: url("svg/drawer-tab.svg");
    --drawer-tab-icon--hover--mask-image: url("svg/drawer-tab-hover.svg");
    --drawer-tab-inverted-icon--mask-image: url("svg/drawer-tab-invert.svg");
    --drawer-tab-inverted-icon--hover--mask-image: url("svg/drawer-tab-invert-hover.svg");
    --plugin--background--pattern: url("svg/bg-pattern.png"); /* Consider removing or adjusting if pattern clashes with solid bg */
    --sidebar--background: var(--plugin--background)
        var(--plugin--background--pattern);

    /* --- Plugin Selector Icons (Unchanged) --- */
    --plugin-selector-candlestick--content: url("svg/mega-menu-icons-candlestick.svg");
    --plugin-selector-heatmap--content: url("svg/mega-menu-icons-heatmap.svg");
    --plugin-selector-map-scatter--content: url("svg/mega-menu-icons-map-scatter.svg");
    --plugin-selector-ohlc--content: url("svg/mega-menu-icons-ohlc.svg");
    --plugin-selector-sunburst--content: url("svg/mega-menu-icons-sunburst.svg");
    --plugin-selector-treemap--content: url("svg/mega-menu-icons-treemap.svg");
    --plugin-selector-x-bar--content: url("svg/mega-menu-icons-x-bar.svg");
    --plugin-selector-x-y-line--content: url("svg/mega-menu-icons-x-y-line.svg");
    --plugin-selector-x-y-scatter--content: url("svg/mega-menu-icons-x-y-scatter.svg");
    --plugin-selector-y-area--content: url("svg/mega-menu-icons-y-area.svg");
    --plugin-selector-y-bar--content: url("svg/mega-menu-icons-y-bar.svg");
    --plugin-selector-y-line--content: url("svg/mega-menu-icons-y-line.svg");
    --plugin-selector-y-scatter--content: url("svg/mega-menu-icons-y-scatter.svg");
    --plugin-selector-datagrid--content: url("svg/mega-menu-icons-datagrid.svg");

    /* --- Base Light Theme Styles (Mostly overridden by Dark Theme below) --- */
    color: #161616;
    background-color: transparent; /* Keep transparent for potential layering */
    --icon--color: #161616;
    --inactive--color: #ababab;
    --inactive--border-color: #dadada;
    --root--background: #ffffff; /* Overridden */
    --active--color: #2670a9;
    --error--color: #ff471e;
    --plugin--background: #ffffff; /* Overridden */
    --overflow-hint-icon--color: rgba(0, 0, 0, 0.2);
    --select--background-color: none;
    --column-drop-container--background: none;
    --warning--background: #042121; /* Overridden */
    --warning--color: #fdfffd; /* Overridden */
    --overflow-hint-icon--color: #fdfffd; /* Overridden */
    --column-style-open-button--content: "style";
    --column-style-close-button--content: ">\00a0 style";
    --tree-label-collapse--content: "-";
    --tree-label-expand--content: "+";

    /* --- Toolbar Icons (Unchanged) --- */
    --toolbar-scroll-lock--content: url("/svg/free-scroll-icon.svg");
    --toolbar-scroll-lock-active--content: url("/svg/align-scroll-icon.svg");
    --toolbar-edit-mode--read-only--content: url("/svg/read-only-icon.svg");
    --toolbar-edit-mode--edit--content: url("/svg/editable-icon.svg");
    --toolbar-edit-mode--select-row--content: url("/svg/datagrid-select-row.svg");
    --toolbar-edit-mode--select-column--content: url("/svg/datagrid-select-column.svg");
    --toolbar-edit-mode--select-region--content: url("/svg/datagrid-select-region.svg");
}

perspective-viewer,
perspective-dropdown {
    /* --- Label/Content Variables (Unchanged) --- */
    --group-by-label--content: "Group By";
    --split-by-label--content: "Split By";
    --sort-label--content: "Order By";
    --filter-label--content: "Where";
    --transpose-button--content: "Swap";
    --config-button-icon--content: "configure";
    --all-columns-label--content: "All Columns";
    --untitled--content: "untitled";
    --plugin-name-datagrid--content: "Datagrid";
    --plugin-name-treemap--content: "Treemap";
    --plugin-name-sunburst--content: "Sunburst";
    --plugin-name-heatmap--content: "Heatmap";
    --plugin-name-x-bar--content: "X Bar";
    --plugin-name-y-bar--content: "Y Bar";
    --plugin-name-y-line--content: "Y Line";
    --plugin-name-x-y-line--content: "X/Y Line";
    --plugin-name-x-y-scatter--content: "X/Y Scatter";
    --plugin-name-y-scatter--content: "Y Scatter";
    --plugin-name-y-area--content: "Y Area";
    --plugin-name-ohlc--content: "OHLC";
    --plugin-name-candlestick--content: "Candlestick";
    --column-selector-column-columns--content: "Columns";
    --column-selector-column-x-axis--content: "X Axis";
    --column-selector-column-y-axis--content: "Y Axis";
    --column-selector-column-color--content: "Color";
    --column-selector-column-size--content: "Size";
    --column-selector-column-symbol--content: "Symbol";
    --column-selector-column-label--content: "Label";
    --column-selector-column-tooltip--content: "Tooltip";
    --add-expression-button--content: "New Column";
    --no-results--content: "Invalid Column";
    --datagrid-column-edit-button--content: "Edit";
    --copy-button--content: "Copy";
    --export-button--content: "Export";
    --reset-button--content: "Reset";
    --edit-mode--read-only--content: "Read Only";
    --edit-mode--edit--content: "Text Edit";
    --edit-mode--select-row--content: "Select Row";
    --edit-mode--select-column--content: "Select Column";
    --edit-mode--select-region--content: "Select Region";
    --scroll-lock-toggle--content: "Free Scroll";
    --scroll-lock-alt-toggle--content: "Align Scroll";
    --color-label--content: "Color";
    --format-label--content: "Format";
    --timezone-label--content: "Timezone";
    --date-style-label--content: "Date Style";
    --time-style-label--content: "Time Style";
    --foreground-label--content: "Foreground";
    --background-label--content: "Background";
    --series-label--content: "Series";
    --color-range-label--content: "Color Range";
    --style-label--content: "Style";
    --minimum-integer-digits-label--content: "Minimum Integer Digits";
    --rounding-increment-label--content: "Rounding Increment";
    --notation-label--content: "Notation";
    --use-grouping-label--content: "Use Grouping";
    --sign-display-label--content: "Sign Display";
    --max-value-label--content: "Max Value";
    --rounding-priority-label--content: "Rounding Priority";
    --rounding-mode-label--content: "Rounding Mode";
    --trailing-zero-display-label--content: "Trailing Zero Display";
    --fractional-digits-label--content: "Fractional Digits";
    --significant-digits-label--content: "Significant Digits";
    --year-label--content: "Year";
    --month-label--content: "Month";
    --day-label--content: "Day";
    --weekday-label--content: "Weekday";
    --hour-label--content: "Hour";
    --minute-label--content: "Minute";
    --second-label--content: "Second";
    --fractional-seconds-label--content: "Fractional Seconds";
    --hours-label--content: "12/24 Hours";
    --style-tab-label--content: "Style";
    --attributes-tab-label--content: "Attributes";
    --debug-tab-label--content: "Debug JSON";
}

/* Theme Name Indicator */
perspective-viewer,
perspective-viewer { /* NOTE: Duplicated selector in original, keeping for consistency */
    --theme-name: "Pro Dark Enhanced"; /* Updated theme name */
}

/* --- Light Theme Styles for perspective-viewer --- */
perspective-viewer {
    /* Light theme colors */
    background-color: #ffffff;
    color: #161616;
    --icon--color: #161616;
    --active--color: #2670a9;
    --error--color: #ff471e;
    --inactive--color: #6b7280;
    --inactive--border-color: #d1d5db;
    --plugin--background: #ffffff;
    --root--background: #ffffff;
    --warning--background: #fef3c7;
    --warning--color: #92400e;
    --select-arrow--background-image: var(--select-arrow-dark--background-image);
}

/* --- Dark Theme Overrides for perspective-viewer --- */
.dark perspective-viewer {
    /* --- Layout/Sizing Variables (Unchanged) --- */
    --button--font-size: 16px;
    --config-button--padding: 15px 8px 6px 8px;
    --column-drop-label--font-size: 8px;
    --column-drop-container--padding: 0px;
    --column-selector--width: 20px;
    --column-selector--font-size: 16px;
    --column_type--width: 25px;
    --select--padding: 0px;
    --top-panel-row--display: inline-flex;
    --button--min-width: 33px;

    /* --- Light Theme Defaults (Overridden below) --- */
    /* color: #161616; */
    /* background-color: transparent; */
    /* --icon--color: #161616; */
    /* --inactive--color: #ababab; */
    /* --inactive--border-color: #dadada; */
    /* --root--background: #ffffff; */
    /* --active--color: #2670a9; */
    /* --error--color: #ff471e; */
    /* --plugin--background: #ffffff; */
    /* --overflow-hint-icon--color: rgba(0, 0, 0, 0.2); */
    /* --select--background-color: none; */
    /* --column-drop-container--background: none; */
    /* --warning--background: #042121; */
    /* --warning--color: #fdfffd; */
    /* --select-arrow--background-image: var(--select-arrow-dark--background-image); */
    /* --overflow-hint-icon--color: #fdfffd; */

    /* --- Font (Unchanged) --- */
    font-family: "Inter var", "ui-monospace", "SFMono-Regular", "SF Mono", "Menlo", "Consolas", "Liberation Mono", monospace;
    --interface-monospace--font-family: "Inter var", "ui-monospace", "SFMono-Regular", "SF Mono", "Menlo", "Consolas", "Liberation Mono", monospace;

    /* --- d3fc Light Theme Defaults (Overridden below) --- */
    /* --d3fc-y1-label--content: "arrow_upward"; */
    /* --d3fc-y2-label--content: "arrow_downward"; */
    /* --d3fc-treedata-axis--lines: none; */
    /* --d3fc-tooltip--background--color: rgba(155, 155, 155, 0.8); */
    /* --d3fc-tooltip--color: #161616; */
    /* --d3fc-tooltip--border-color: #fff; */
    /* --d3fc-tooltip--box-shadow: 0 2px 4px 0 rgb(0 0 0 / 10%); */
    /* --d3fc-gridline--color: #eaedef; */
    /* --d3fc-axis-ticks--color: #161616; */
    /* --d3fc-axis--lines: #c5c9d0; */
    /* --d3fc-legend--background: var(--plugin--background); */
    /* --d3fc-series: rgba(31, 119, 180, 0.8); */
    /* --d3fc-series-1: #0366d6; ... etc */
    /* --d3fc-full--gradient: linear-gradient(...); */
    /* --d3fc-positive--gradient: linear-gradient(...); */
    /* --d3fc-negative--gradient: linear-gradient(...); */
    /* --rt-pos-cell--color: #338dcd; */
    /* --rt-neg-cell--color: #ff471e; */
    /* --map-tile-url: "http://{a-c}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}.png"; */
    /* --map-element-background: #fff; */
    /* --map-category-1: #0366d6; ... etc */
    /* --map-gradient: linear-gradient(...); */

    /* === Dark Theme Enhancements === */
    background-color: #030712; /* MODIFIED: Set to Tailwind bg-gray-950 */
    color: white;
    --icon--color: white;
    --active--color: #2770a9; /* Keep existing active color */
    --error--color: #ff9485;
    --inactive--color: #61656e; /* Use original dark inactive color */
    --inactive--border-color: #1f2937; /* Use original dark border color (relevant for non-workspace elements) */
    --plugin--background: #030712; /* MODIFIED: Set to Tailwind bg-gray-900 */
    --root--background: #030712; /* MODIFIED: Ensure root matches */
    --modal-target--background: rgba(255, 255, 255, 0.05); /* Keep existing */
    --active--background: rgba(39, 113, 170, 0.5); /* Keep existing */
    --expression--operator-color: #c5c9d0;
    --expression--function-color: #22a0ce;
    --expression--error-color: rgb(255, 136, 136);
    --calendar--filter: invert(1);
    --warning--color: #030712; /* MODIFIED: Warning text color to contrast with new background */
    --warning--background: var(--icon--color); /* White background for warning */
    --select-arrow--background-image: var(--select-arrow-light--background-image); /* Use light arrow on dark bg */
    --code-editor-symbol--color: white;
    --code-editor-literal--color: #7dc3f0;
    --code-editor-operator--color: rgb(23, 166, 123);
    --code-editor-comment--color: rgb(204, 120, 48);
    --code-editor-column--color: #e18ee1;
    --rt-pos-cell--color: #7dc3f0;
    --rt-neg-cell--color: #ff9485;

    /* === d3fc Brand Color Updates === */
    --d3fc-legend--text: #B2B7BD;         /* MODIFIED: Use Neutral Light Gray */
    --d3fc-treedata--labels: #E5E7E9;      /* MODIFIED: Use Neutral Off-White */
    --d3fc-treedata--hover-highlight: #FFFFFF; /* MODIFIED: Use White */
    --d3fc-tooltip--color: #FFFFFF;        /* MODIFIED: Use White text in tooltip */
    --d3fc-axis-ticks--color: #A2A4A4;     /* MODIFIED: Use Neutral Silver */
    --d3fc-axis--lines: #4C5864;          /* MODIFIED: Use Neutral Slate Gray */
    --d3fc-gridline--color: #1f2937;       /* MODIFIED: Use Neutral Gray Blue (Subtle) */
    --d3fc-tooltip--background: #1D262F;   /* MODIFIED: Use Primary Dark Slate */
    --d3fc-tooltip--border-color: #31404E; /* MODIFIED: Use Primary Dark Slate */
    --d3fc-legend--background: var(--plugin--background); /* Inherits #030712 */

    /* -- d3fc series colors - Mapped from Brand Palette -- */
    --d3fc-series:   #00B5AD; /* 1. Primary Teal */
    --d3fc-series-1: #00B5AD; /* 1. Primary Teal */
    --d3fc-series-2: #1B9BC2; /* 2. Secondary Blue */
    --d3fc-series-3: #EE6969; /* 3. Secondary Coral */
    --d3fc-series-4: #F7BD63; /* 4. Secondary Yellow */
    --d3fc-series-5: #A06ACD; /* 5. Secondary Purple */
    --d3fc-series-6: #008682; /* 6. Primary Dark Teal */
    --d3fc-series-7: #A2A4A4; /* 7. Neutral Silver */
    --d3fc-series-8: #E5E7E9; /* 8. Neutral Off-White */
    --d3fc-series-9: #4C5864; /* 9. Neutral Slate Gray */
    --d3fc-series-10: #31404E;/* 10. Primary Dark Slate */

    /* -- Gradients - Using Brand Colors -- */
    --d3fc-full--gradient:     linear-gradient(#EE6969 0%, #030712 50%, #00B5AD 100%); /* Coral -> Background -> Teal */
    --d3fc-positive--gradient: linear-gradient(#030712 0%, #00B5AD 100%); /* Background -> Teal */
    --d3fc-negative--gradient: linear-gradient(#EE6969 0%, #030712 100%); /* Coral -> Background */

    /* -- Map Styles (Using Brand Colors) -- */
    --map-tile-url: "http://{a-c}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}.png";
    --map-attribution--filter: invert(1) hue-rotate(180deg);
    --map-element-background: #1D262F; /* MODIFIED: Use Primary Dark Slate */
    /* -- Map category colors - Mapped from Brand Palette (matching series) -- */
    --map-category-1:  #00B5AD; /* 1. Primary Teal */
    --map-category-2:  #1B9BC2; /* 2. Secondary Blue */
    --map-category-3:  #EE6969; /* 3. Secondary Coral */
    --map-category-4:  #F7BD63; /* 4. Secondary Yellow */
    --map-category-5:  #A06ACD; /* 5. Secondary Purple */
    --map-category-6:  #008682; /* 6. Primary Dark Teal */
    --map-category-7:  #A2A4A4; /* 7. Neutral Silver */
    --map-category-8:  #E5E7E9; /* 8. Neutral Off-White */
    --map-category-9:  #4C5864; /* 9. Neutral Slate Gray */
    --map-category-10: #31404E; /* 10. Primary Dark Slate */
    /* -- Map Gradient - Using Brand Colors -- */
    --map-gradient: linear-gradient(#EE6969 0%, #030712 50%, #00B5AD 100%); /* Coral -> Background -> Teal */
}

/* --- Light Theme for Menus/Dropdowns --- */
perspective-copy-menu,
perspective-export-menu,
perspective-dropdown,
perspective-date-column-style,
perspective-datetime-column-style,
perspective-number-column-style,
perspective-string-column-style {
    background-color: #ffffff;
    color: #161616;
    border: 1px solid #d1d5db;
    --icon--color: #161616;
    --active--color: #2670a9;
    --error--color: #ff471e;
    --inactive--color: #6b7280;
    --inactive--border-color: #d1d5db;
    --plugin--background: #ffffff;
    --warning--color: #92400e;
    --warning--background: #fef3c7;
    --select-arrow--background-image: var(--select-arrow-dark--background-image);
}

/* --- Dark Theme for Menus/Dropdowns --- */
.dark perspective-copy-menu,
.dark perspective-export-menu,
.dark perspective-dropdown,
perspective-date-column-style,
perspective-datetime-column-style,
perspective-number-column-style,
perspective-string-column-style {
    /* --- Font (Inherited/Unchanged) --- */
    /* font-family: "ui-monospace", ... */

    /* --- Light Theme Defaults (Overridden) --- */
    /* background-color: white; */
    /* border: 1px solid var(--inactive--color); */
    /* border-radius: 0 0 2px 2px; */
    /* color: #161616; */
    /* --icon--color: #161616; */
    /* --inactive--color: #ababab; */
    /* --inactive--border-color: #dadada; */
    /* --root--background: #ffffff; */
    /* --active--color: #2670a9; */
    /* --error--color: #ff471e; */
    /* --plugin--background: #ffffff; */
    /* --warning--background: #042121; */
    /* --warning--color: #fdfffd; */
    /* --select-arrow--background-image: var(--select-arrow-dark--background-image); */

    /* --- Base settings --- */
    border-radius: 0 0 2px 2px; /* Keep rounding */
    --column-style-pos-color--content: "+";
    --column-style-neg-color--content: "-";
    --save-button-icon--content: "save";
    --reset-button-icon--content: "refresh";
    font-family: "ui-monospace", "SFMono-Regular", "SF Mono", "Menlo", "Consolas", "Liberation Mono", monospace;
    --interface-monospace--font-family: "ui-monospace", "SFMono-Regular", "SF Mono", "Menlo", "Consolas", "Liberation Mono", monospace;

    /* === Dark Theme Enhancements === */
    background-color: #1f2937; /* MODIFIED: Slightly lighter than main bg (Tailwind bg-gray-800) for menus */
    color: white;
    border: 1px solid #374151; /* MODIFIED: Subtle border (Tailwind border-gray-700) */
    --icon--color: white;
    --active--color: #2770a9; /* Keep existing active */
    --error--color: #ff9485; /* Keep existing error */
    --inactive--color: #61656e; /* Keep existing inactive */
    --inactive--border-color: #4c505b; /* Keep existing inactive border */
    --plugin--background: #1f2937; /* MODIFIED: Match menu background */
    --modal-target--background: rgba(255, 255, 255, 0.05); /* Keep existing */
    --active--background: rgba(39, 113, 170, 0.5); /* Keep existing */
    --expression--operator-color: #c5c9d0;
    --expression--function-color: #22a0ce;
    --expression--error-color: rgb(255, 136, 136);
    --calendar--filter: invert(1);
    --warning--color: #1f2937; /* MODIFIED: Contrast with warning bg */
    --warning--background: var(--icon--color); /* White warning bg */
    --select-arrow--background-image: var(--select-arrow-light--background-image); /* Light arrow */
    --code-editor-symbol--color: white;
    --code-editor-literal--color: #7dc3f0;
    --code-editor-operator--color: rgb(23, 166, 123);
    --code-editor-comment--color: rgb(204, 120, 48);
    --code-editor-column--color: #e18ee1;

    /* Final override for menu background/border */
    background-color: #1f2937; /* MODIFIED: Ensure bg-gray-800 */
    border: 1px solid #374151; /* MODIFIED: Ensure border-gray-700 */
}

/* Host select arrow */
:host select {
    background-image: url("/svg/chevron-down.svg");
    filter: invert(1); /* MODIFIED: Ensure dropdown arrows are visible on dark background */
}

/* Workspace theme indicator */
perspective-workspace,
perspective-workspace, /* NOTE: Duplicated selector in original */
perspective-indicator {
    --theme-name: "Pro Dark Enhanced"; /* Updated theme name */
}

/* Status bar height */
perspective-workspace perspective-viewer {
    --status-bar--height: 39px;
}

/* Style viewer inside workspace when settings are open */
perspective-workspace perspective-viewer[settings] {
    --modal-panel--margin: -4px 0 -4px 0;
    --status-bar--border-radius: 6px 0 0 0;
    --main-column--margin: 3px 0 3px 3px;
    /* MODIFIED: Remove border for cleaner look */
    --main-column--border: none;
    --main-column--border-width: 0px;
    --main-column--border-radius: 6px 0 0 6px;
    --settings-button--margin: 10px 0 0 0;
}

/* --- Light Theme for Workspace --- */
perspective-workspace {
    background-color: #ffffff;
    color: #161616;
    --workspace-split-panel-handle--background-color: #ffffff;
    --icon--color: #161616;
    --active--color: #2670a9;
    --error--color: #ff471e;
    --inactive--color: #6b7280;
    --inactive--border-color: #d1d5db;
    --plugin--background: #ffffff;
    --root--background: #ffffff;
    --warning--color: #92400e;
    --warning--background: #fef3c7;
    --workspace-tabbar--background-color: #ffffff;
}

/* --- Workspace Dark Theme Enhancements --- */
.dark perspective-workspace {
    /* --- Font (Unchanged) --- */
    font-family: "ui-monospace", "SFMono-Regular", "SF Mono", "Menlo", "Consolas", "Liberation Mono", monospace;
    /* --- Content/Icon Variables (Unchanged) --- */
    --open-settings-button--content: "expand_more";
    --close-settings-button--content: "expand_less";
    --close-button--content: "\2715";
    --master-divider--background-color: #243136; /* Keep original divider */
    --menu-maximize--content: "fullscreen";
    --menu-minimize--content: "fullscreen_exit";
    --menu-duplicate--content: "call_split";
    --menu-master--content: "cast";
    --menu-detail--content: "notes";
    --menu-export--content: "file_download";
    --menu-copy--content: "file_copy";
    --menu-reset--content: "autorenew";
    --menu-link--content: "link";
    --menu-unlink--content: "link_off";
    --menu-newmenu--content: "add";
    --menu-close--content: "close";
    --menu-new--content: "description";
    --menu-newview--content: "file_copy";
    --column-drag-handle--mask-image: url("/svg/drag-handle.svg");
    --bookmarks--mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMTEiIHZpZXdCb3g9IjAgMCAyNCAxMSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICAgIDxwYXRoIGQ9Ik0zLjY2MjE0IDYuOTY0NzZMMC41IDkuODYzMzhWMC41SDcuNVY5Ljg2MzM4TDQuMzM3ODYgNi45NjQ3Nkw0IDYuNjU1MDVMMy42NjIxNCA2Ljk2NDc2WiIgc3Ryb2tlPSIjRkZGRkZGIi8+CiAgICA8cGF0aCBkPSJNMTkgNEwyMSA2TDIzIDQiIHN0cm9rZT0iI0ZGRkZGRiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+Cjwvc3ZnPgo="); /* MODIFIED: Changed stroke to white for visibility */

    /* --- Tab Bar Border Removal --- */
    --workspace-tabbar--border: none; /* MODIFIED: Remove border */
    --workspace-tabbar--border-width: 0px; /* MODIFIED: Remove border */
    --workspace-tabbar--border-radius: 0px 0px 6px 6px; /* Keep rounding */
    /* --workspace-tabbar--border-color: var(--inactive--color); */ /* No longer needed */
    --workspace-tabbar-tab--border-width: 1px 1px 0 1px; /* Keep top/side borders for tabs */

    /* --- Background & Color Settings --- */
    /* background-color: #242526; */ /* Overridden below */
    /* --workspace-split-panel-handle--background-color: #242526; */ /* Overridden below */
    /* color: white; */ /* Set below */
    /* --icon--color: white; */ /* Set below */
    /* --active--color: #2770a9; */ /* Set below */
    /* --error--color: #ff9485; */ /* Set below */
    /* --inactive--color: #61656e; */ /* Set below */
    /* --inactive--border-color: #4c505b; */ /* Set below */
    /* --plugin--background: #242526; */ /* Set below */
    /* --modal-target--background: rgba(255, 255, 255, 0.05); */ /* Set below */
    /* --active--background: rgba(39, 113, 170, 0.5); */ /* Set below */
    /* --expression--operator-color: #c5c9d0; */ /* Set below */
    /* --expression--function-color: #22a0ce; */ /* Set below */
    /* --expression--error-color: rgb(255, 136, 136); */ /* Set below */
    /* --calendar--filter: invert(1); */ /* Set below */
    /* --warning--color: #242526; */ /* Set below */
    /* --warning--background: var(--icon--color); */ /* Set below */
    /* --select-arrow--background-image: var(--select-arrow-light--background-image); */ /* Set below */
    /* --code-editor-symbol--color: white; */ /* Set below */
    /* --code-editor-literal--color: #7dc3f0; */ /* Set below */
    /* --code-editor-operator--color: rgb(23, 166, 123); */ /* Set below */
    /* --code-editor-comment--color: rgb(204, 120, 48); */ /* Set below */
    /* --code-editor-column--color: #e18ee1; */ /* Set below */

    /* === Final Dark Theme Workspace Overrides === */
    background-color: #030712; /* MODIFIED: Set to Tailwind bg-gray-900 */
    color: white;
    --workspace-split-panel-handle--background-color: #030712; /* MODIFIED: Match background */
    --icon--color: white;
    --active--color: #2770a9;
    --error--color: #ff9485;
    --inactive--color: #6b7280; /* MODIFIED: Tailwind gray-500 for better contrast on bg-gray-900 */
    --inactive--border-color: #374151; /* MODIFIED: Tailwind gray-700 for tab borders */
    --plugin--background: #030712; /* MODIFIED: Match background */
    --root--background: #030712; /* MODIFIED: Match background */
    --modal-target--background: rgba(255, 255, 255, 0.05);
    --active--background: rgba(39, 113, 170, 0.5);
    --expression--operator-color: #d1d5db; /* MODIFIED: Tailwind gray-300 */
    --expression--function-color: #22a0ce; /* Keep original blue */
    --expression--error-color: rgb(255, 136, 136);
    --calendar--filter: invert(1);
    --warning--color: #030712; /* Text color for warning */
    --warning--background: var(--icon--color); /* White warning background */
    --select-arrow--background-image: var(--select-arrow-light--background-image);
    --code-editor-symbol--color: white;
    --code-editor-literal--color: #7dc3f0;
    --code-editor-operator--color: rgb(23, 166, 123);
    --code-editor-comment--color: rgb(204, 120, 48);
    --code-editor-column--color: #e18ee1;
    --workspace-tabbar--background-color: #030712; /* MODIFIED: Match main background */
    --workspace-secondary--color: #9ca3af; /* MODIFIED: Tailwind gray-400 */
    --workspace-tabbar--border-color: var(--inactive--border-color); /* Uses #374151 */
    --workspace-tabbar-tab--border-width: 1px 1px 0 1px; /* Keep tab borders */
}

/* Ensure viewer background within master widget matches workspace */
perspective-viewer.workspace-master-widget {
    --plugin--background: #030712; /* MODIFIED: Match workspace background */
}

/* Workspace Menu */
perspective-workspace-menu {
    font-family: "ui-monospace", "SFMono-Regular", "SF Mono", "Menlo", "Consolas", "Liberation Mono", monospace;
    font-weight: 300;
    background: #1f2937 !important; /* MODIFIED: Tailwind bg-gray-800 for menu */
    color: white !important;
    border: 1px solid #374151 !important; /* MODIFIED: Subtle border (Tailwind border-gray-700) */
}