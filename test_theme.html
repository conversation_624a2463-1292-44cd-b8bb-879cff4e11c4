<!DOCTYPE html>
<html class="overflow-hidden">
<head>
    <title>Theme Test</title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'selector'
        }
    </script>
</head>
<body class="bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 flex h-screen antialiased">
    <!-- Sidebar -->
    <div class="flex z-[100] relative">
        <div class="flex h-screen w-16 flex-col justify-between border-e bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-800 overflow-visible">
            <div>
                <!-- Logo -->
                <div class="inline-flex h-16 w-16 items-center justify-center">
                    <div class="mx-auto h-6 w-6 bg-primary-600 rounded"></div>
                </div>
                
                <!-- Menu Items -->
                <div class="border-t border-gray-200 dark:border-gray-800">
                    <div class="px-2 py-4 space-y-1">
                        <div class="group relative flex justify-center rounded-md px-2 py-1.5 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100 transition-all duration-150">
                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Bottom Section -->
            <div class="sticky inset-x-0 bottom-0 border-t border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900">
                <!-- Theme Toggle Button -->
                <div class="p-2">
                    <button id="theme-toggle" 
                            class="group relative flex w-full justify-center rounded-lg px-2 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-200">
                        <!-- Sun Icon (visible in dark mode) -->
                        <svg id="sun-icon" class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                        </svg>
                        <!-- Moon Icon (visible in light mode) -->
                        <svg id="moon-icon" class="h-5 w-5 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                        </svg>
                        <span class="invisible absolute start-full top-1/2 ms-4 -translate-y-1/2 rounded bg-gray-800 dark:bg-gray-700 px-2 py-1.5 text-xs font-medium text-white group-hover:visible">
                            Toggle theme
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Main Content -->
    <main class="w-full h-full overflow-y-auto bg-gray-50 dark:bg-gray-950">
        <!-- Header -->
        <header class="border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 shadow-sm mt-6 md:mt-0">
            <div class="max-w-7xl px-4 h-16 flex items-center sm:px-6 lg:px-8 mx-auto">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between w-full">
                    <div class="flex items-center gap-3">
                        <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100 sm:text-3xl sm:tracking-tight">
                            Theme Test Page
                        </h2>
                        <span class="inline-flex items-center rounded-md bg-gray-100 dark:bg-gray-800 px-2.5 py-0.5 text-sm font-medium text-gray-700 dark:text-gray-200">
                            Testing
                        </span>
                    </div>
                </div>
            </div>
        </header>
        
        <!-- Content -->
        <div class="p-8">
            <div class="max-w-4xl mx-auto">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">Light/Dark Mode Test</h3>
                <p class="text-gray-700 dark:text-gray-300 mb-6">
                    This page demonstrates the light and dark mode functionality. Click the theme toggle button in the sidebar to switch between modes.
                </p>
                
                <!-- Test Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                        <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Card 1</h4>
                        <p class="text-gray-600 dark:text-gray-400">This is a test card to show how content looks in both light and dark modes.</p>
                    </div>
                    
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                        <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Card 2</h4>
                        <p class="text-gray-600 dark:text-gray-400">Another test card with different content to verify styling consistency.</p>
                    </div>
                </div>
                
                <!-- Button Examples -->
                <div class="mt-8 space-x-4">
                    <button class="bg-primary-600 dark:bg-primary-800 hover:bg-primary-700 dark:hover:bg-primary-900 text-white px-4 py-2 rounded-md">
                        Primary Button
                    </button>
                    <button class="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 px-4 py-2 rounded-md">
                        Secondary Button
                    </button>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Theme controller functionality
        class ThemeController {
            constructor() {
                this.darkIcon = document.getElementById('sun-icon');
                this.lightIcon = document.getElementById('moon-icon');
                this.toggleButton = document.getElementById('theme-toggle');
                
                this.init();
            }
            
            init() {
                // Check for saved user preference, default to dark mode if not set
                const savedTheme = localStorage.getItem('darkMode');
                const darkMode = savedTheme !== null ? savedTheme === 'true' : true;
                this.updateTheme(darkMode);
                
                // Add click listener
                this.toggleButton.addEventListener('click', () => this.toggle());
            }
            
            toggle() {
                const isDark = document.documentElement.classList.contains('dark');
                this.updateTheme(!isDark);
            }
            
            updateTheme(darkMode) {
                // Update DOM
                if (darkMode) {
                    document.documentElement.classList.add('dark');
                    this.darkIcon.classList.add('hidden');
                    this.lightIcon.classList.remove('hidden');
                } else {
                    document.documentElement.classList.remove('dark');
                    this.darkIcon.classList.remove('hidden');
                    this.lightIcon.classList.add('hidden');
                }
                
                // Update color scheme for native elements
                document.documentElement.style.colorScheme = darkMode ? 'dark' : 'light';
                
                // Save preference
                localStorage.setItem('darkMode', darkMode);
            }
        }
        
        // Initialize theme controller when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            new ThemeController();
        });
    </script>
</body>
</html>
